'use client';

import React, { useState, useEffect } from 'react';
import { Form, InputNumber, Row, Col, Card, Space, message, Image, Tag, Avatar, Alert, Divider } from 'antd';
import { AppstoreOutlined, FormOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { evaluate } from 'mathjs';
import { perfLog, apiCallTracker } from '@/lib/utils/perfLog';
import { CalculationState, BoxBasicInfo } from '../../types/calculation';
import { boxApi } from '@/services/adminApi';
import { Box, BoxType, BoxPart, BoxFormula } from '@/types/box';
import { translateChineseToPinyin } from '@/lib/utils/formula';

// 固定属性定义
const FIXED_ATTRIBUTES = [
  { id: -1, name: '咬口', code: 'yakou', value: 15 },
  { id: -2, name: '拉规', code: 'lagui', value: 10 },
  { id: -3, name: '出血', code: 'chuxue', value: 4 }
];

interface BasicInfoStepProps {
  state: CalculationState;
  onUpdate: {
    basicInfo: (data: Partial<BoxBasicInfo>) => void;
  };
  onRecalculate: () => void;
  sourceBoxId?: number;
  sourceBox?: Box | null;
  pageLoading?: boolean; // 添加页面级别的加载状态
}

// 公式计算结果接口
interface FormulaResult {
  formula: BoxFormula;
  result: number | null;
  error?: string;
}

// 部件公式计算结果接口
interface PartFormulaResults {
  part: BoxPart;
  results: FormulaResult[];
}

/**
 * 基础信息步骤组件
 */
const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  state,
  onUpdate,
  onRecalculate,
  sourceBoxId,
  sourceBox: propSourceBox,
  pageLoading = false
}) => {
  const [form] = Form.useForm();
  const [sourceBox, setSourceBox] = useState<Box | null>(null);
  const [loading, setLoading] = useState(false);
  const [formulaResults, setFormulaResults] = useState<PartFormulaResults[]>([]);

  // 获取来源盒型详情
  const fetchSourceBoxDetail = async (boxId: number) => {
    const apiKey = `box-getDetail-${boxId}`;

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('盒型详情API调用被阻止，防止重复调用');
      return;
    }

    try {
      setLoading(true);
      const result = await boxApi.getDetail(boxId);

      if (result.success && result.data) {
        const boxData = result.data;
        setSourceBox(boxData);

        // 自动填充基础信息
        const calculationName = `${boxData.name}_计算_${new Date().toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })}`;

        // 处理动态属性，填充默认值
        const dynamicAttributes = (boxData.attributes || []).map(attr => ({
          ...attr,
          value: attr.value || 0  // 使用默认值或0
        }));

        // 合并固定属性和动态属性
        const allAttributes = [
          ...FIXED_ATTRIBUTES,
          ...dynamicAttributes
        ];

        const formData = {
          name: calculationName,
          quantity: 1000, // 默认数量
          attributes: allAttributes,
          boxType: boxData.boxType,  // 保存盒子类型
          parts: boxData.parts       // 保存部件信息
        };

        // 设置表单值，包括固定属性和动态属性
        const formValues: any = {
          name: formData.name,
          quantity: formData.quantity,
        };

        // 为每个属性设置表单字段值
        allAttributes.forEach(attr => {
          formValues[`attr_${attr.code}`] = attr.value;
        });

        form.setFieldsValue(formValues);
        onUpdate.basicInfo(formData);
      } else {
        message.error('获取盒型详情失败');
      }
    } catch (error) {
      perfLog.error('获取盒型详情错误:', error);
      message.error('获取盒型详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 优先使用传入的 sourceBox，避免重复 API 调用
  useEffect(() => {
    perfLog.debug('BasicInfoStep useEffect 触发', {
      hasSourceBox: !!sourceBox,
      hasPropSourceBox: !!propSourceBox,
      sourceBoxId,
      loading
    });

    // 如果已经有 sourceBox 数据（无论是传入的还是已加载的），就不再重复处理
    if (sourceBox) {
      perfLog.debug('已有 sourceBox 数据，跳过处理');
      return;
    }

    if (propSourceBox) {
      perfLog.debug('使用传入的 propSourceBox 数据');
      // 使用传入的 sourceBox 数据
      setSourceBox(propSourceBox);

      // 自动填充基础信息
      const calculationName = `${propSourceBox.name}_计算_${new Date().toLocaleString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })}`;

      // 处理动态属性，填充默认值
      const dynamicAttributes = (propSourceBox.attributes || []).map(attr => ({
        ...attr,
        value: attr.value || 0  // 使用默认值或0
      }));

      // 合并固定属性和动态属性
      const allAttributes = [
        ...FIXED_ATTRIBUTES,
        ...dynamicAttributes
      ];

      const formData = {
        name: calculationName,
        quantity: 1000, // 默认数量
        attributes: allAttributes,
        boxType: propSourceBox.boxType,  // 保存盒子类型
        parts: propSourceBox.parts       // 保存部件信息
      };

      // 设置表单值，包括固定属性和动态属性
      const formValues: any = {
        name: formData.name,
        quantity: formData.quantity,
      };

      // 为每个属性设置表单字段值
      allAttributes.forEach(attr => {
        formValues[`attr_${attr.code}`] = attr.value;
      });

      form.setFieldsValue(formValues);
      onUpdate.basicInfo(formData);
    } else if (sourceBoxId && !propSourceBox && !loading && !pageLoading) {
      // 延迟检查，给页面级别的API调用一些时间
      const timer = setTimeout(() => {
        // 再次检查是否已经有数据了
        if (!sourceBox && !propSourceBox) {
          perfLog.debug('延迟检查后仍无数据，调用API获取盒型详情');
          fetchSourceBoxDetail(sourceBoxId);
        } else {
          perfLog.debug('延迟检查发现已有数据，跳过API调用');
        }
      }, 100); // 延迟100ms

      return () => clearTimeout(timer);
    } else if (!sourceBoxId) {
      // 没有sourceBoxId时，初始化固定属性
      const formData = {
        name: '',
        quantity: 1000,
        description: '',
        attributes: FIXED_ATTRIBUTES
      };

      // 设置表单固定属性的默认值
      const formValues: any = {};
      FIXED_ATTRIBUTES.forEach(attr => {
        formValues[`attr_${attr.code}`] = attr.value;
      });
      form.setFieldsValue(formValues);
      onUpdate.basicInfo(formData);
    }
  }, [sourceBoxId, propSourceBox, sourceBox, loading, pageLoading]);

  // 计算部件公式
  const calculatePartFormulas = () => {
    if (!sourceBox || !sourceBox.parts || !state.basicInfo.attributes) {
      return;
    }

    // 检查属性是否都已填写
    const allAttributesFilled = state.basicInfo.attributes.every(attr =>
      typeof attr.value === 'number' && attr.value >= 0
    );

    if (!allAttributesFilled) {
      setFormulaResults([]);
      return;
    }

    // 构建计算变量作用域
    const scope: Record<string, number> = {
      数量: state.basicInfo.quantity || 0
    };

    // 添加属性到作用域
    state.basicInfo.attributes.forEach(attr => {
      scope[attr.code] = attr.value || 0;
      scope[attr.name] = attr.value || 0;
    });

    const results: PartFormulaResults[] = [];

    sourceBox.parts.forEach(part => {
      if (part.formulas && part.formulas.length > 0) {
        const partResults: FormulaResult[] = [];

        part.formulas.forEach(formula => {
          if (formula.expression && formula.expression.trim()) {
            try {
              const translatedExpression = translateChineseToPinyin(formula.expression);
              const result = evaluate(translatedExpression, scope);
              partResults.push({
                formula,
                result: typeof result === 'number' ? result : null,
                error: typeof result !== 'number' ? '计算结果不是数字' : undefined
              });
            } catch (error: any) {
              partResults.push({
                formula,
                result: null,
                error: `计算错误: ${error?.message || '未知错误'}`
              });
            }
          } else {
            partResults.push({
              formula,
              result: null,
              error: '公式表达式为空'
            });
          }
        });

        results.push({
          part,
          results: partResults
        });
      }
    });

    setFormulaResults(results);
  };

  // 当属性值变化时重新计算公式
  useEffect(() => {
    calculatePartFormulas();
  }, [state.basicInfo.attributes, state.basicInfo.quantity, sourceBox]);

  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 获取所有属性（固定属性 + 动态属性）
    const allAttributes = [
      ...FIXED_ATTRIBUTES,
      ...(sourceBox?.attributes || [])
    ];

    // 处理属性值变化
    const updatedAttributes = allAttributes.map(attr => {
      const fieldName = `attr_${attr.code}`;
      return {
        ...attr,
        value: allValues[fieldName] !== undefined ? allValues[fieldName] : attr.value || 0
      };
    });

    // 构建更新的基础信息，保持关键字段
    const updatedBasicInfo = {
      name: allValues.name,
      quantity: allValues.quantity,
      description: allValues.description,
      attributes: updatedAttributes,
      // 保持重要字段
      boxType: state.basicInfo.boxType,
      parts: state.basicInfo.parts
    };

    onUpdate.basicInfo(updatedBasicInfo);
    // 自动重新计算
    setTimeout(onRecalculate, 100);
  };

  // 获取来源盒型的第一张图片
  const getSourceBoxImage = () => {
    if (!sourceBox || !sourceBox.images || sourceBox.images.length === 0) {
      return null;
    }
    return boxApi.getImageUrl(sourceBox.images[0].id || 0);
  };

  // 渲染属性输入框
  const renderAttributeInputs = () => {
    const fixedAttributeRows = [];
    const dynamicAttributeRows = [];

    // 渲染固定属性（每行3个）
    fixedAttributeRows.push(
      <Row key="fixed" gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <div style={{ 
            background: '#f0f9ff', 
            border: '1px solid #bae6fd', 
            borderRadius: '6px', 
            padding: '12px 16px',
            marginBottom: '16px'
          }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#0c4a6e', fontWeight: '600' }}>
              固定属性
            </h4>
            <p style={{ margin: 0, color: '#0369a1', fontSize: '13px' }}>
              这些是通用的印刷工艺参数，您可以根据具体需求调整
            </p>
          </div>
        </Col>
        {FIXED_ATTRIBUTES.map((attr) => (
          <Col key={attr.id} xs={24} sm={4}>
            <Form.Item
              name={`attr_${attr.code}`}
              label={`${attr.name}`}
              rules={[{ required: true, message: `请输入${attr.name}` }]}
            >
              <InputNumber
                placeholder={`请输入${attr.name}`}
                style={{ width: '100%' }}
                min={0}
                step={0.1}
                precision={2}
                addonAfter="mm"
              />
            </Form.Item>
          </Col>
        ))}
      </Row>
    );

    // 渲染动态属性
    if (!sourceBox || !sourceBox.attributes || sourceBox.attributes.length === 0) {
      return (
        <div>
          {fixedAttributeRows}
          <div style={{ 
            background: '#fafafa', 
            border: '1px dashed #d9d9d9', 
            borderRadius: '6px', 
            padding: '24px',
            textAlign: 'center',
            color: '#666'
          }}>
            <p style={{ margin: 0, fontSize: '14px' }}>该盒型没有定义动态属性</p>
            <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#999' }}>
              只需填写上方固定属性即可进行计算
            </p>
          </div>
        </div>
      );
    }

    // 动态属性标题和说明
    dynamicAttributeRows.push(
      <Row key="dynamic-header" style={{ marginBottom: 16 }}>
        <Col span={24}>
          <div style={{ 
            background: '#f6ffed', 
            border: '1px solid #b7eb8f', 
            borderRadius: '6px', 
            padding: '12px 16px'
          }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#389e0d', fontWeight: '600' }}>
              盒型属性
            </h4>
            <p style={{ margin: 0, color: '#52c41a', fontSize: '13px' }}>
              以下属性来自盒型模板定义，请根据实际需求填写具体数值
            </p>
          </div>
        </Col>
      </Row>
    );

    // 按每行6个动态属性排列
    const attributes = sourceBox.attributes;
    for (let i = 0; i < attributes.length; i += 6) {
      const rowAttributes = attributes.slice(i, i + 6);
      dynamicAttributeRows.push(
        <Row key={`dynamic-${i}`} gutter={[16, 16]}>
          {rowAttributes.map((attr) => (
            <Col key={attr.id} xs={24} sm={4}>
              <Form.Item
                name={`attr_${attr.code}`}
                label={`${attr.name}`}
                rules={[{ required: true, message: `请输入${attr.name}` }]}
              >
                <InputNumber
                  placeholder={`请输入${attr.name}`}
                  style={{ width: '100%' }}
                  min={0}
                  step={0.1}
                  precision={2}
                  addonAfter="mm"
                />
              </Form.Item>
            </Col>
          ))}
          {/* 填充空列 */}
          {rowAttributes.length < 6 && Array.from({ length: 6 - rowAttributes.length }).map((_, idx) => (
            <Col key={`empty_dynamic_${i}_${idx}`} xs={24} sm={4} />
          ))}
        </Row>
      );
    }

    return (
      <div>
        {fixedAttributeRows}
        <Divider style={{ margin: '32px 0 24px 0' }} />
        {dynamicAttributeRows}
      </div>
    );
  };

  // 渲染部件公式计算结果
  const renderFormulaResults = () => {
    if (!sourceBox || !sourceBox.parts || sourceBox.parts.length === 0) {
      return (
        <div className="text-center text-gray-500 py-8">
          <p>该盒型没有定义部件</p>
        </div>
      );
    }

    // 检查属性是否都已填写
    const allAttributesFilled = state.basicInfo.attributes?.every(attr =>
      typeof attr.value === 'number' && attr.value >= 0
    ) ?? false;

    if (!allAttributesFilled) {
      return (
        <Alert
          message="请先完成属性填写"
          description="需要填写完所有属性后才能进行公式计算"
          type="warning"
          showIcon
          icon={<ExclamationCircleOutlined />}
        />
      );
    }

    if (formulaResults.length === 0) {
      return (
        <div className="text-center text-gray-500 py-8">
          <p>该盒型的部件没有定义公式</p>
        </div>
      );
    }

    return (
      <Row>
        {formulaResults.map((partResult, index) => (
          <Col key={index} span={12}>
            <Card
              size="small"
              title={
                <Space>
                  <FormOutlined />
                  <span>{partResult.part.name}</span>
                </Space>
              }
            >
              {partResult.results.map((formulaResult, fIndex) => (
                <div key={fIndex}>
                  <Row align="middle">
                    <Col span={6}>
                      <span className="font-medium">{formulaResult.formula.name}:</span>
                    </Col>
                    <Col span={10}>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {formulaResult.formula.expression || '无表达式'}
                      </code>
                    </Col>
                    <Col span={8}>
                      {formulaResult.error ? (
                        <span className="text-red-600 text-sm">
                          <ExclamationCircleOutlined style={{ marginRight: 4 }} />
                          {formulaResult.error}
                        </span>
                      ) : formulaResult.result !== null ? (
                        <span className="text-green-600 font-semibold">
                          <CheckCircleOutlined style={{ marginRight: 4 }} />
                          {formulaResult.result.toFixed(2)}
                        </span>
                      ) : (
                        <span className="text-gray-500">未计算</span>
                      )}
                    </Col>
                  </Row>
                  {fIndex < partResult.results.length - 1 && <Divider style={{ margin: '8px 0' }} />}
                </div>
              ))}
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <div className="basic-info-step">
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        {/* 1. 盒型信息展示 */}
        {sourceBox && (
          <Card title="盒型信息" className="mb-6" style={{ marginBottom: 16 }}>
            <Row gutter={[24, 16]} align="middle">
              <Col xs={24} sm={8} md={6}>
                <div className="text-center">
                  {getSourceBoxImage() ? (
                    <Image
                      width={150}
                      height={150}
                      src={getSourceBoxImage()!}
                      alt={sourceBox.name}
                      style={{
                        objectFit: 'contain',
                        border: '1px solid #d9d9d9',
                        borderRadius: '8px',
                        background: '#fafafa'
                      }}
                      fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RUG8O+L"
                    />
                  ) : (
                    <Avatar size={150} style={{ backgroundColor: '#f0f0f0' }}>
                      <AppstoreOutlined style={{ fontSize: '60px' }} />
                    </Avatar>
                  )}
                </div>
              </Col>
              <Col xs={24} sm={16} md={18}>
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  <Row>
                    <Col span={12}>
                      <h3 style={{ margin: 0, fontSize: '20px', fontWeight: 'bold' }}>
                        {sourceBox.name}
                      </h3>
                      <Tag
                        color={sourceBox.boxType === BoxType.PAPER_BOX ? 'blue' : 'purple'}
                        style={{ marginTop: '8px', fontSize: '14px', padding: '4px 12px' }}
                      >
                        {sourceBox.boxType === BoxType.PAPER_BOX ? '纸盒' : '精装盒'}
                      </Tag>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="quantity"
                        label="数量"
                        rules={[{ required: true, message: '请输入数量' }]}
                      >
                        <InputNumber
                          min={1}
                          placeholder="请输入数量"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={[24, 12]}>
                    <Col span={12}>
                      <div>
                        <span className="text-gray-600" style={{ fontSize: '14px' }}>加工费：</span>
                        <span style={{ fontSize: '16px', fontWeight: '500' }}>
                          {sourceBox.processingFee ? `¥${sourceBox.processingFee}` : '未设置'}
                        </span>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div>
                        <span className="text-gray-600" style={{ fontSize: '14px' }}>起步价：</span>
                        <span style={{ fontSize: '16px', fontWeight: '500' }}>
                          {sourceBox.processingBasePrice ? `¥${sourceBox.processingBasePrice}` : '未设置'}
                        </span>
                      </div>
                    </Col>
                  </Row>
                </Space>
              </Col>
            </Row>
          </Card>
        )}
        {/* 2. 属性填写 */}
        <Card title="属性填写" style={{ marginBottom: 16 }}>
          {sourceBox ? (
            <div>
              {renderAttributeInputs()}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              <p>请先选择盒型模板</p>
            </div>
          )}
        </Card>

        {/* 3. 部件公式计算结果 */}
        <Card title="部件公式计算结果" className="mb-6">
          {renderFormulaResults()}
        </Card>
      </Form>
    </div>
  );
};

export default BasicInfoStep; 