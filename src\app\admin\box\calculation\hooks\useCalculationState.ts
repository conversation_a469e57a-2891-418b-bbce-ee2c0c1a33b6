import { useState, useCallback } from 'react';
import { perfLog } from '@/lib/utils/perfLog';
import {
  CalculationState,
  CalculationStep,
  BoxBasicInfo,
  PartConfig,
  PackagingConfig,
  MaterialConfig,
  ProcessConfig,
  AccessoryConfig,
  ProcessingFeeConfig,
  FormulaConfig,
  QuotationDetail
} from '../types/calculation';
import { validateAllPartMaterialConfigs } from '../validations/materials';

// 初始状态
const initialState: CalculationState = {
  currentStep: CalculationStep.BASIC_INFO,
  basicInfo: {
    quantity: 1,
    attributes: []
  },
  partConfig: {
    parts: [],
    mergedParts: [],
    totalArea: 0
  },
  packagingConfig: {
    marginLength: 3,
    marginWidth: 3,
    enableMaxImposition: true,
    arrangementMode: 'normal',
    enableOptimization: true,
    enablePartMerging: false,
    selectedMergeGroups: [],
    hasCalculatedImposition: false // 添加拼版计算状态标记
  },
  materialConfig: {
    papers: [],
    specialPapers: [],
    greyBoards: [],
    BoxMaterials: [],
  },
  processConfig: {
    partGroupProcessConfigs: {},
    processCost: 0,
  },
  accessoryConfig: {
    accessories: [],
    giftBoxAccessories: [],
    accessoryCost: 0
  },
  processingFeeConfig: {
    customFees: [],
    fixedFees: [],
    processingFeeCost: 0
  },
  formulaConfig: {
    formulas: [],
    additionalFees: [],
    formulaCost: 0
  },
  quotation: {
    basicInfo: {
      quantity: 1,
      attributes: []
    },
    materialCost: 0,
    processCost: 0,
    accessoryCost: 0,
    processingFeeCost: 0,
    formulaCost: 0,
    totalCost: 0
  },
  isCalculating: false,
  errors: {}
};

/**
 * 计算状态管理Hook
 */
export function useCalculationState() {
  const [state, setState] = useState<CalculationState>(initialState);

  // 更新基础信息
  const updateBasicInfo = useCallback((basicInfo: Partial<BoxBasicInfo>) => {
    setState(prev => ({
      ...prev,
      basicInfo: { ...prev.basicInfo, ...basicInfo }
    }));
  }, []);

  // 更新部件配置
  const updatePartConfig = useCallback((partConfig: Partial<PartConfig>) => {
    setState(prev => ({
      ...prev,
      partConfig: { ...prev.partConfig, ...partConfig }
    }));
  }, []);

  // 更新拼版配置
  const updatePackagingConfig = useCallback((packagingConfig: Partial<PackagingConfig>) => {
    setState(prev => ({
      ...prev,
      packagingConfig: { ...prev.packagingConfig, ...packagingConfig }
    }));
  }, []);

  // 更新材料配置
  const updateMaterialConfig = useCallback((materialConfig: Partial<MaterialConfig>) => {
    perfLog.debug('updateMaterialConfig 被调用:', materialConfig);
    setState(prev => {
      const newState = {
        ...prev,
        materialConfig: { ...prev.materialConfig, ...materialConfig }
      };
      perfLog.debug('updateMaterialConfig 更新后的状态:', {
        oldMaterialConfig: prev.materialConfig,
        newMaterialConfig: newState.materialConfig,
        partMaterialConfigs: newState.materialConfig.partMaterialConfigs
      });
      return newState;
    });
  }, []);

  // 更新工艺配置
  const updateProcessConfig = useCallback((processConfig: Partial<ProcessConfig>) => {
    setState(prev => ({
      ...prev,
      processConfig: { ...prev.processConfig, ...processConfig }
    }));
  }, []);

  // 更新配件配置
  const updateAccessoryConfig = useCallback((accessoryConfig: Partial<AccessoryConfig>) => {
    setState(prev => ({
      ...prev,
      accessoryConfig: { ...prev.accessoryConfig, ...accessoryConfig }
    }));
  }, []);

  // 更新加工费配置
  const updateProcessingFeeConfig = useCallback((processingFeeConfig: Partial<ProcessingFeeConfig>) => {
    setState(prev => ({
      ...prev,
      processingFeeConfig: { ...prev.processingFeeConfig, ...processingFeeConfig }
    }));
  }, []);

  // 更新公式配置
  const updateFormulaConfig = useCallback((formulaConfig: Partial<FormulaConfig>) => {
    setState(prev => ({
      ...prev,
      formulaConfig: { ...prev.formulaConfig, ...formulaConfig }
    }));
  }, []);

  // 更新报价明细
  const updateQuotation = useCallback((quotation: Partial<QuotationDetail>) => {
    setState(prev => ({
      ...prev,
      quotation: { ...prev.quotation, ...quotation }
    }));
  }, []);

  // 设置当前步骤
  const setCurrentStep = useCallback((step: CalculationStep) => {
    setState(prev => ({
      ...prev,
      currentStep: step
    }));
  }, []);

  // 设置计算状态
  const setIsCalculating = useCallback((isCalculating: boolean) => {
    setState(prev => ({
      ...prev,
      isCalculating
    }));
  }, []);

  // 设置错误信息
  const setError = useCallback((field: string, error: string) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: error
      }
    }));
  }, []);

  // 清除错误信息
  const clearError = useCallback((field: string) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [field]: ''
      }
    }));
  }, []);

  // 清除所有错误
  const clearAllErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      errors: {}
    }));
  }, []);

  // 重置状态
  const resetState = useCallback(() => {
    setState(initialState);
  }, []);

  // 获取当前步骤的验证状态
  const getStepValidation = useCallback((step: CalculationStep): boolean => {
    switch (step) {
      case CalculationStep.BASIC_INFO:
        // 检查基本信息
        const hasBasicInfo = state.basicInfo.quantity > 0;

        // 检查动态属性是否都已填写
        const hasAllAttributes = !state.basicInfo.attributes?.length ||
          state.basicInfo.attributes.every(attr =>
            typeof attr.value === 'number' && attr.value >= 0
          );

        return hasBasicInfo && hasAllAttributes;
      case CalculationStep.PACKAGING:
        // 检查拼版配置
        const hasValidPackaging = Boolean(state.packagingConfig.marginLength >= 0 && state.packagingConfig.marginWidth >= 0);

        // 检查是否已执行拼版计算
        const hasCalculatedImposition = Boolean(state.packagingConfig.hasCalculatedImposition);

        // 如果还没有执行拼版计算，不允许进入下一步
        if (!hasCalculatedImposition) {
          perfLog.debug('PACKAGING步骤验证: 未执行拼版计算');
          return false;
        }

        // 检查是否有需要材料的部件组
        const facePartGroups = state.packagingConfig.facePartGroups || [];
        const greyPartGroups = state.packagingConfig.greyPartGroups || [];
        const hasPartGroups = facePartGroups.length > 0 || greyPartGroups.length > 0;

        // 如果没有部件组，只需要拼版配置有效
        if (!hasPartGroups) {
          return hasValidPackaging && hasCalculatedImposition;
        }

        // 检查部件材料配置是否完整
        const materialValidation = validateAllPartMaterialConfigs(
          state.materialConfig.partMaterialConfigs,
          facePartGroups,
          greyPartGroups
        );

        perfLog.debug('PACKAGING步骤验证:', {
          hasValidPackaging,
          hasCalculatedImposition,
          hasPartGroups,
          facePartGroupsCount: facePartGroups.length,
          greyPartGroupsCount: greyPartGroups.length,
          materialValidation,
          partMaterialConfigs: state.materialConfig.partMaterialConfigs
        });

        return hasValidPackaging && hasCalculatedImposition && materialValidation.isValid;
      case CalculationStep.PROCESS:
        return true; // 工艺选择是可选的
      case CalculationStep.ACCESSORY:
        return true; // 配件选择是可选的
      case CalculationStep.PROCESSING_FEE:
        return true; // 加工选择是可选的
      case CalculationStep.QUOTATION:
        return state.quotation.totalCost >= 0;
      default:
        return false;
    }
  }, [state]);

  return {
    state,
    updateBasicInfo,
    updatePartConfig,
    updatePackagingConfig,
    updateMaterialConfig,
    updateProcessConfig,
    updateAccessoryConfig,
    updateProcessingFeeConfig,
    updateFormulaConfig,
    updateQuotation,
    setCurrentStep,
    setIsCalculating,
    setError,
    clearError,
    clearAllErrors,
    resetState,
    getStepValidation
  };
} 
