# 瓦楞材料损耗率配置功能更新

## 更新概述

在原有的拼板损耗率配置功能基础上，新增了瓦楞材料（corrugated）的专门损耗率配置参数，使得四种主要材料类型（面纸、特种纸、灰板、瓦楞材料）都有独立的损耗率配置。

## 更新内容

### 1. 类型定义扩展

**文件：** `src/app/admin/box/calculation/types/calculation.ts`

在 `PackagingConfig` 接口中添加了瓦楞材料损耗率参数：

```typescript
// 损耗率配置参数
paperWasteRate?: number;         // 面纸损耗率（默认10%）
specialPaperWasteRate?: number;  // 特种纸损耗率（默认5%）
greyBoardWasteRate?: number;     // 灰板损耗率（默认8%）
corrugatedWasteRate?: number;    // 瓦楞材料损耗率（默认7%）
```

### 2. 拼板参数配置界面更新

**文件：** `src/app/admin/box/calculation/components/steps/PackagingStep.tsx`

#### 2.1 默认值常量更新
添加了瓦楞材料损耗率的默认值：

```typescript
const DEFAULT_VALUES = {
  BLEED: 4,
  GRIPPER: 10,
  BITE: 15,
  MARGIN_LENGTH: 3,
  MARGIN_WIDTH: 3,
  PAPER_WASTE_RATE: 10,      // 面纸损耗率 10%
  SPECIAL_PAPER_WASTE_RATE: 5, // 特种纸损耗率 5%
  GREY_BOARD_WASTE_RATE: 8,  // 灰板损耗率 8%
  CORRUGATED_WASTE_RATE: 7   // 瓦楞材料损耗率 7%
} as const;
```

#### 2.2 弹窗布局优化
将原来的单行四列布局改为2行2列布局，提供更好的用户体验：

- **第一行**：面纸损耗率、特种纸损耗率
- **第二行**：灰板损耗率、瓦楞材料损耗率

#### 2.3 表单处理更新
- 弹窗打开时包含瓦楞材料损耗率的初始值设置
- 表单验证包含瓦楞材料损耗率的验证规则
- 保存配置时包含瓦楞材料损耗率的更新

#### 2.4 参数传递更新
将瓦楞材料损耗率参数传递给 `ImpositionLogicDisplay` 组件：

```typescript
corrugatedWasteRate={state.packagingConfig.corrugatedWasteRate ?? DEFAULT_VALUES.CORRUGATED_WASTE_RATE}
```

### 3. 材料成本计算逻辑更新

**文件：** `src/app/admin/box/calculation/components/ImpositionLogicDisplay.tsx`

#### 3.1 接口扩展
在 `ImpositionLogicDisplayProps` 接口中添加了瓦楞材料损耗率参数：

```typescript
// 损耗率参数
paperWasteRate?: number; // 面纸损耗率（%）
specialPaperWasteRate?: number; // 特种纸损耗率（%）
greyBoardWasteRate?: number; // 灰板损耗率（%）
corrugatedWasteRate?: number; // 瓦楞材料损耗率（%）
```

#### 3.2 损耗率应用逻辑更新
修改了损耗率获取逻辑，让瓦楞材料使用专门的损耗率参数：

```typescript
const getWasteRate = (materialCategory: string): number => {
  switch (materialCategory) {
    case 'paper':
      return paperWasteRate / 100;
    case 'specialPaper':
      return specialPaperWasteRate / 100;
    case 'greyBoard':
      return greyBoardWasteRate / 100;
    case 'corrugated':
      return corrugatedWasteRate / 100; // 使用专门的瓦楞材料损耗率
    default:
      return paperWasteRate / 100;
  }
};
```

#### 3.3 依赖项更新
在 useMemo 的依赖项中添加了瓦楞材料损耗率参数，确保参数变化时重新计算。

### 4. 计算引擎更新

**文件：** `src/app/admin/box/calculation/utils/calculationEngine.ts`

添加了专门的瓦楞材料费用计算方法：

```typescript
/**
 * 计算瓦楞材料费用
 */
private calculateCorrugatedCost(corrugated: any, packagingConfig: PackagingConfig): number {
  const basePrice = corrugated.unitPrice * corrugated.quantity;
  // 从配置中获取瓦楞材料损耗率，如果没有配置则使用默认值7%
  const wasteRate = (packagingConfig.corrugatedWasteRate ?? 7) / 100;
  return basePrice * (1 + wasteRate);
}
```

## 功能特点

### 1. 完整的材料类型覆盖
- **面纸材料**：使用面纸损耗率（默认10%）
- **特种纸材料**：使用特种纸损耗率（默认5%）
- **灰板材料**：使用灰板损耗率（默认8%）
- **瓦楞材料**：使用瓦楞材料损耗率（默认7%）

### 2. 优化的用户界面
- **2行2列布局**：更合理的空间利用和视觉效果
- **分类清晰**：相关材料类型分组显示
- **操作便捷**：保持原有的弹窗操作模式

### 3. 合理的默认值
- **瓦楞材料损耗率**：默认7%，处于建议范围5-9%的中间值
- **参数范围**：与其他材料保持一致（0-50%）
- **精度控制**：支持0.1%的精度设置

### 4. 向后兼容性
- **可选参数**：瓦楞材料损耗率为可选参数，不影响现有功能
- **默认值处理**：如果没有配置，使用合理的默认值
- **类型安全**：严格的 TypeScript 类型定义

## 使用说明

### 1. 配置瓦楞材料损耗率
1. 在盒型计算的第二步"拼版参数"中
2. 点击"损耗率配置"按钮打开配置弹窗
3. 在弹窗的第二行右侧找到"瓦楞材料损耗率"配置项
4. 根据实际需要调整瓦楞材料的损耗率（建议范围5-9%）
5. 点击"确定"保存配置

### 2. 损耗率应用
- 瓦楞材料现在使用专门的损耗率参数进行计算
- 不再与灰板材料共享损耗率设置
- 支持所有计价单位（元/张、元/平方、元/吨）

### 3. 建议值参考
根据行业经验，建议的损耗率范围：
- **面纸**：8-12%（默认10%）
- **特种纸**：3-8%（默认5%）
- **灰板**：6-10%（默认8%）
- **瓦楞材料**：5-9%（默认7%）

## 技术优势

### 1. 独立配置
- 每种材料类型都有独立的损耗率配置
- 避免了瓦楞材料与灰板材料损耗率混用的问题
- 提供更精确的成本计算

### 2. 界面优化
- 2行2列布局提供更好的视觉效果
- 相关材料类型合理分组
- 保持界面简洁和操作便捷

### 3. 类型安全
- 完整的 TypeScript 类型定义
- 严格的参数验证和默认值处理
- 避免运行时错误

### 4. 扩展性
- 为未来可能新增的材料类型提供了良好的扩展基础
- 模块化的设计便于维护和更新

## 注意事项

1. **参数同步**：瓦楞材料损耗率参数在整个计算流程中保持一致性
2. **实时更新**：修改损耗率后需要重新计算拼版以应用新的参数
3. **材料分类**：确保瓦楞材料正确分类为 'corrugated' 类型
4. **默认值**：如果没有配置瓦楞材料损耗率，系统将使用7%的默认值
5. **向后兼容**：现有的配置和功能不受影响，新参数为可选项
