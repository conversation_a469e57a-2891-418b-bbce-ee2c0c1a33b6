import { z } from 'zod';

// 材料选择校验规则
export const selectedMaterialSchema = z.object({
  material: z.object({
    id: z.number(),
    name: z.string(),
    price: z.number(),
    unit: z.string(),
  }),
  quantity: z.number().min(0, '数量不能为负数'),
  unit: z.string().min(1, '单位不能为空'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  totalPrice: z.number().min(0, '总价不能为负数'),
});

// 材料规格选项校验规则
export const materialSpecOptionSchema = z.object({
  value: z.string(),
  label: z.string(),
  size: z.object({
    width: z.number(),
    height: z.number(),
  }).optional(),
  available: z.boolean(),
  price: z.number().optional(),
});

// 部件材料配置校验规则
export const partMaterialConfigSchema = z.object({
  partGroupId: z.string(),
  materialCategory: z.enum(['paper', 'specialPaper']).optional(),
  materialId: z.number({
    required_error: '请选择材料',
    invalid_type_error: '材料选择无效'
  }),
  materialName: z.string({
    required_error: '材料名称不能为空'
  }),
  materialSpec: z.string({
    required_error: '请选择材料规格',
    invalid_type_error: '材料规格选择无效'
  }),
  specOptions: z.array(materialSpecOptionSchema).optional(),
  materialSize: z.object({
    width: z.number(),
    height: z.number(),
  }).optional(),
  // 面纸必须选择打印机
  printingMachineId: z.number().optional(),
  printingMachineName: z.string().optional(),
  printingMachineMaxLength: z.number().optional(),
  printingMachineMaxWidth: z.number().optional(),
});

// 面纸部件材料配置校验规则（必须包含打印机）
export const facePaperMaterialConfigSchema = partMaterialConfigSchema.extend({
  printingMachineId: z.number({
    required_error: '面纸必须选择打印机',
    invalid_type_error: '打印机选择无效'
  }),
  printingMachineName: z.string({
    required_error: '打印机名称不能为空'
  }),
});

// 灰板纸部件材料配置校验规则（不需要打印机）
export const greyBoardMaterialConfigSchema = partMaterialConfigSchema.omit({
  materialCategory: true,
  printingMachineId: true,
  printingMachineName: true,
  printingMachineMaxLength: true,
  printingMachineMaxWidth: true,
});

// 材料配置校验规则
export const materialConfigSchema = z.object({
  papers: z.array(selectedMaterialSchema).optional(),
  specialPapers: z.array(selectedMaterialSchema).optional(),
  greyBoards: z.array(selectedMaterialSchema).optional(),
  materialCost: z.number().min(0).optional(),
  // 部件材料配置映射
  partMaterialConfigs: z.record(z.string(), partMaterialConfigSchema).optional(),
});

// 验证函数：检查面纸材料配置是否完整
export const validateFacePaperConfig = (config: any): boolean => {
  try {
    facePaperMaterialConfigSchema.parse(config);
    return true;
  } catch {
    return false;
  }
};

// 验证函数：检查灰板纸材料配置是否完整
export const validateGreyBoardConfig = (config: any): boolean => {
  try {
    greyBoardMaterialConfigSchema.parse(config);
    return true;
  } catch {
    return false;
  }
};

// 验证函数：检查所有部件材料配置是否完整
export const validateAllPartMaterialConfigs = (
  partMaterialConfigs: Record<string, any> | undefined,
  facePartGroups: any[] = [],
  greyPartGroups: any[] = []
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!partMaterialConfigs) {
    if (facePartGroups.length > 0) {
      errors.push('面纸部件需要选择材料');
    }
    if (greyPartGroups.length > 0) {
      errors.push('灰板纸部件需要选择材料');
    }
    return { isValid: false, errors };
  }

  // 验证面纸部件组
  for (const group of facePartGroups) {
    const config = partMaterialConfigs[group.id];
    if (!config) {
      errors.push(`面纸组"${group.name}"未配置材料`);
      continue;
    }

    if (!validateFacePaperConfig(config)) {
      const missingFields = [];
      if (!config.materialId) missingFields.push('材料');
      if (!config.materialSpec) missingFields.push('规格');
      if (!config.printingMachineId) missingFields.push('打印机');

      errors.push(`面纸组"${group.name}"缺少：${missingFields.join('、')}`);
    }
  }

  // 验证灰板纸部件组
  for (const group of greyPartGroups) {
    const config = partMaterialConfigs[group.id];
    if (!config) {
      errors.push(`灰板纸组"${group.name}"未配置材料`);
      continue;
    }

    if (!validateGreyBoardConfig(config)) {
      const missingFields = [];
      if (!config.materialId) missingFields.push('材料');
      if (!config.materialSpec) missingFields.push('规格');

      errors.push(`灰板纸组"${group.name}"缺少：${missingFields.join('、')}`);
    }
  }

  return { isValid: errors.length === 0, errors };
};

export type SelectedMaterialForm = z.infer<typeof selectedMaterialSchema>;
export type MaterialConfigForm = z.infer<typeof materialConfigSchema>;
export type PartMaterialConfigForm = z.infer<typeof partMaterialConfigSchema>;
export type FacePaperMaterialConfigForm = z.infer<typeof facePaperMaterialConfigSchema>;
export type GreyBoardMaterialConfigForm = z.infer<typeof greyBoardMaterialConfigSchema>;