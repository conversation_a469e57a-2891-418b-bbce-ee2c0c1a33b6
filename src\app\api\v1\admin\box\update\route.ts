import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateBoxSchema, UpdateBoxParams } from '@/lib/validations/admin/box';
import { withValidation, assert, assertExists } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';


export const POST = withValidation<UpdateBoxParams>(
  updateBoxSchema,
  async (request: NextRequest, validatedData: UpdateBoxParams) => {
    const data = validatedData;

    // 检查盒型是否存在
    const existingBox = await prisma.box.findFirst({
      where: {
        id: data.id,
        isDel: false,
      },
    });

    assertExists(existingBox, ErrorCode.BOX_NOT_FOUND, '盒型不存在');

    // 如果修改了名称，检查名称是否重复
    if (data.name && data.name !== existingBox!.name) {
      const duplicateBox = await prisma.box.findFirst({
        where: {
          name: data.name,
          id: { not: data.id },
          isDel: false,
        },
      });

      assert(!duplicateBox, ErrorCode.BOX_NAME_EXISTS, '盒型名称已存在');
    }

    // 开始数据库事务
    const result = await prisma.$transaction(async (tx) => {
      // 更新盒型基本信息
      const box = await tx.box.update({
        where: { id: data.id },
        data: {
          name: data.name,
          status: data.status,
          description: data.description,
          boxType: data.boxType,
          processingFee: data.processingFee,
          processingBasePrice: data.processingBasePrice,
        },
      });

      // 更新属性 - 先删除旧的，再创建新的
      if (data.attributes !== undefined) {
        await tx.boxAttribute.deleteMany({
          where: { boxId: data.id },
        });

        if (data.attributes.length > 0) {
          await tx.boxAttribute.createMany({
            data: data.attributes.map((attr) => ({
              boxId: data.id,
              name: attr.name,
              code: attr.code,
              value: attr.value,
            })),
          });
        }
      }

      // 更新部件和公式 - 先删除旧的，再创建新的
      if (data.parts !== undefined) {
        // 删除旧的部件相关数据
        await tx.boxFormula.deleteMany({
          where: { boxId: data.id },
        });
        await tx.boxPart.deleteMany({
          where: { boxId: data.id },
        });

        // 创建新的部件和公式
        if (data.parts.length > 0) {
          for (const part of data.parts) {
            const createdPart = await tx.boxPart.create({
              data: {
                boxId: data.id,
                name: part.name,
              },
            });

            if (part.formulas && part.formulas.length > 0) {
              // 验证必填公式
              const requiredFormulas = part.formulas.filter((formula: any) => formula.isRequired);
              for (const requiredFormula of requiredFormulas) {
                assert(
                  !!(requiredFormula.expression && requiredFormula.expression.trim()),
                  ErrorCode.BOX_FORMULA_INVALID,
                  `必填公式"${requiredFormula.name}"不能为空`
                );
              }

              await tx.boxFormula.createMany({
                data: part.formulas.map((formula) => ({
                  boxId: data.id,
                  partId: createdPart.id,
                  name: formula.name,
                  expression: formula.expression || '',
                  isRequired: !!formula.isRequired,
                })),
              });
            }
          }
        }
      }

      // 更新打包公式
      if (data.packaging !== undefined) {
        // 删除旧的打包公式
        await tx.boxPackaging.deleteMany({
          where: { boxId: data.id },
        });

        // 创建新的打包公式
        if (data.packaging) {
          await tx.boxPackaging.create({
            data: {
              boxId: data.id,
              lengthFormula: data.packaging.lengthFormula,
              widthFormula: data.packaging.widthFormula,
              heightFormula: data.packaging.heightFormula,
            },
          });
        }
      }

      // 更新图片
      if (data.images !== undefined) {
        // 删除旧的图片
        await tx.boxImage.deleteMany({
          where: { boxId: data.id },
        });

        // 创建新的图片
        if (data.images.length > 0) {
          for (const image of data.images) {
            // 验证图片数据
            assert(!!image.imageData, ErrorCode.BOX_IMAGE_INVALID, '图片数据不能为空');
            assert(!!image.mimeType, ErrorCode.BOX_IMAGE_INVALID, '图片类型不能为空');

            await tx.boxImage.create({
              data: {
                boxId: data.id,
                name: image.name,
                imageData: Buffer.from(image.imageData, 'base64'),
                mimeType: image.mimeType,
                sortOrder: image.sortOrder || 0,
              },
            });
          }
        }
      }

      return box;
    });

    return successResponse(result, '更新盒型成功');
  }
); 