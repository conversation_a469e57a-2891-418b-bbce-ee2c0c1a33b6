// 定价和费用计算相关类型定义

// 费用类型枚举
export enum CostType {
  MATERIAL = 'material',        // 材料费用
  PRINTING = 'printing',        // 印刷费用
  PROCESS = 'process',          // 工艺费用
  ACCESSORY = 'accessory',      // 配件费用
  PROCESSING = 'processing',    // 加工费
  SETUP = 'setup',              // 起版费
  DESIGN = 'design',            // 设计费
  SAMPLE = 'sample',            // 打样费
  CUTTING = 'cutting',          // 模切费
  PACKAGING_FEE = 'packaging',  // 打包费
  SHIPPING = 'shipping',        // 运费
  FORMULA = 'formula',          // 公式费用
  OTHER = 'other'               // 其他费用
}

// 费用明细项
export interface CostItem {
  id?: string;                  // 费用项ID
  type: CostType;               // 费用类型
  name: string;                 // 费用名称
  description?: string;         // 费用描述
  quantity: number;             // 数量
  unit: string;                 // 单位
  unitPrice: number;            // 单价
  totalPrice: number;           // 总价
  formula?: string;             // 计算公式
  parameters?: Record<string, any>; // 计算参数
  isRequired: boolean;          // 是否必需
  sortOrder?: number;           // 排序
}

// 费用分组
export interface CostGroup {
  type: CostType;               // 费用类型
  name: string;                 // 分组名称
  items: CostItem[];            // 费用项列表
  subtotal: number;             // 小计
  discountRate?: number;        // 折扣率
  discountAmount?: number;      // 折扣金额
  finalAmount: number;          // 最终金额
}

// 报价汇总
export interface PricingSummary {
  costGroups: CostGroup[];      // 费用分组
  subtotal: number;             // 小计
  totalDiscount: number;        // 总折扣
  taxRate: number;              // 税率
  taxAmount: number;            // 税额
  finalTotal: number;           // 最终总价
  currency: string;             // 货币单位
  validUntil?: string;          // 报价有效期
}

// 价格计算参数
export interface PricingParameters {
  materialWasteRate: number;    // 材料损耗率
  setupCostRatio: number;       // 起版费比例
  profitMargin: number;         // 利润率
  discountRate: number;         // 折扣率
  taxRate: number;              // 税率
  minimumOrderValue: number;    // 最小订单金额
  urgentOrderSurcharge: number; // 加急费率
}

// 材料费用计算
export interface MaterialCostCalculation {
  materialType: string;         // 材料类型
  materialName: string;         // 材料名称
  specification: string;        // 规格
  quantity: number;             // 用量
  unit: string;                 // 单位
  unitPrice: number;            // 单价
  wasteRate: number;            // 损耗率
  actualQuantity: number;       // 实际用量（含损耗）
  totalCost: number;            // 总成本
  costPerUnit: number;          // 单位成本
}

// 工艺费用计算
export interface ProcessCostCalculation {
  processType: string;          // 工艺类型
  processName: string;          // 工艺名称
  area?: number;                // 加工面积
  quantity?: number;            // 加工数量
  complexity: 'simple' | 'medium' | 'complex'; // 复杂程度
  setupCost: number;            // 起版费
  unitCost: number;             // 单位费用
  totalCost: number;            // 总费用
  parameters: Record<string, any>; // 工艺参数
}

// 价格历史记录
export interface PriceHistoryRecord {
  id: string;                   // 记录ID
  timestamp: string;            // 时间戳
  action: 'calculate' | 'update' | 'confirm'; // 操作类型
  oldTotal?: number;            // 原总价
  newTotal: number;             // 新总价
  changes: Array<{              // 变更明细
    item: string;
    oldValue?: number;
    newValue: number;
    reason?: string;
  }>;
  operator?: string;            // 操作人员
  notes?: string;               // 备注
}

// 报价单配置
export interface QuotationConfig {
  template: 'standard' | 'detailed' | 'simple'; // 模板类型
  showFormulas: boolean;        // 显示计算公式
  showParameters: boolean;      // 显示计算参数
  groupByCostType: boolean;     // 按费用类型分组
  includeImages: boolean;       // 包含图片
  watermark?: string;           // 水印
  companyInfo: {                // 公司信息
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    logo?: string;
  };
}

// 价格比较分析
export interface PriceComparison {
  currentQuotation: PricingSummary;    // 当前报价
  alternatives: Array<{                // 替代方案
    name: string;
    summary: PricingSummary;
    savings: number;
    savingsRate: number;
    notes?: string;
  }>;
  recommendations: string[];           // 建议
}

// 成本分析
export interface CostAnalysis {
  totalCost: number;            // 总成本
  costBreakdown: {              // 成本分解
    materialCost: number;       // 材料成本
    laborCost: number;          // 人工成本
    machineCost: number;        // 机器成本
    overheadCost: number;       // 管理费用
    profitMargin: number;       // 利润
  };
  costPerUnit: number;          // 单位成本
  profitability: {             // 盈利性分析
    grossMargin: number;        // 毛利率
    netMargin: number;          // 净利率
    breakEvenQuantity: number;  // 盈亏平衡点
  };
}

// 定价策略
export interface PricingStrategy {
  strategy: 'cost_plus' | 'competitive' | 'value_based' | 'custom'; // 定价策略
  baseCost: number;             // 基础成本
  markupRate: number;           // 加价率
  competitorPrice?: number;     // 竞争对手价格
  valueFactors?: Array<{        // 价值因素
    factor: string;
    weight: number;
    score: number;
  }>;
  finalPrice: number;           // 最终价格
  priceRange: {                 // 价格区间
    min: number;
    max: number;
    recommended: number;
  };
} 