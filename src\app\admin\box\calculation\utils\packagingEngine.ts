// 拼版计算引擎实现

import { evaluate } from 'mathjs';
import { BoxPart, BoxType } from '@/types/box';
import { translateChineseToPinyin } from '@/lib/utils/formula';
import { perfLog } from '@/lib/utils/perfLog';
import {
  MaterialTypeAnalysis,
  PartDimension,
  PartGroup,
  ImpositionParams,
  ImpositionResult,
  ImpositionOptimization,
  PackagingCalculationEngine,
  OptimizationScheme,
  MergeCandidate
} from '../types/packaging';

/**
 * 拼版计算引擎实现
 */
export class PackagingCalculationEngineImpl implements PackagingCalculationEngine {
  /**
   * 根据用户选择的合并组应用合并
   */
  applyUserMergeGroups(parts: BoxPart[], mergeGroups: any[]): BoxPart[] {
    if (!mergeGroups || mergeGroups.length === 0) {
      return parts;
    }

    // 这里可以根据用户选择的合并组来重新组织部件
    // 目前先返回原始部件，后续可以扩展
    return parts;
  }

  /**
   * 分析材料类型 - 根据盒子类型和部件公式判断可选材料
   */
  analyzeMaterialTypes(parts: BoxPart[], boxType?: BoxType): MaterialTypeAnalysis {
    const result: MaterialTypeAnalysis = {
      canSelectFacePaper: false,
      canSelectGreyBoard: false,
      facePartFormulas: [],
      greyPartFormulas: []
    };

    // 纸盒只需要面纸
    if (boxType === BoxType.PAPER_BOX) {
      result.canSelectFacePaper = true;
      parts.forEach(part => {
        if (part.formulas) {
          part.formulas.forEach(formula => {
            if (formula.expression && formula.expression.trim()) {
              result.facePartFormulas.push(formula);
            }
          })
        }
      });
    }

    // 精装盒根据公式名称判断
    if (boxType === BoxType.HARDCOVER_BOX) {
        parts.forEach(part => {
          if (part.formulas) {
            part.formulas.forEach(formula => {
              // 检查公式是否有表达式
              if (formula.expression && formula.expression.trim()) {
                const formulaName = formula.name.toLowerCase();

                // 检查面纸相关公式（基于公式名称）
                if (this.containsFacePaperFormulaName(formulaName)) {
                  result.canSelectFacePaper = true;
                  result.facePartFormulas.push(formula);
                }

                // 检查灰板纸相关公式（基于公式名称）
                if (this.containsGreyBoardFormulaName(formulaName)) {
                  result.canSelectGreyBoard = true;
                  result.greyPartFormulas.push(formula);
                }
              }
            });
          }
        });
      }

      return result;
    }

    /**
     * 计算部件尺寸 - 修正：按材料类型分别计算，避免相互覆盖
     */
    calculatePartDimensions(parts: BoxPart[], attributes: Record<string, number>): PartDimension[] {
      const results: PartDimension[] = [];

      perfLog.debug('开始计算部件尺寸:', {
        partsCount: parts.length,
        attributes: Object.keys(attributes)
      });

      parts.forEach((part, partIndex) => {
        perfLog.debug(`处理部件 ${partIndex}: ${part.name}`);

        if (part.formulas) {
          perfLog.debug(`部件 ${part.name} 有 ${part.formulas.length} 个公式`);

          // 按材料类型分别计算尺寸，避免相互覆盖
          const materialDimensions = this.calculatePartDimensionsByMaterial(part, attributes);

          // 将所有有效的材料类型尺寸添加到结果中
          results.push(...materialDimensions);
        } else {
          perfLog.debug(`部件 ${part.name} 没有公式`);
        }
      });

      perfLog.debug(`最终部件尺寸结果:`, results);
      return results;
    }

    /**
     * 按材料类型分别计算部件尺寸
     */
    private calculatePartDimensionsByMaterial(part: BoxPart, attributes: Record<string, number>): PartDimension[] {
      const results: PartDimension[] = [];

      // 分别计算面纸和灰板纸的尺寸
      const faceResult = this.calculateSingleMaterialDimensions(part, attributes, 'face');
      const greyResult = this.calculateSingleMaterialDimensions(part, attributes, 'grey');

      if (faceResult) {
        results.push(faceResult);
        perfLog.debug(`添加面纸部件尺寸:`, faceResult);
      }

      if (greyResult) {
        results.push(greyResult);
        perfLog.debug(`添加灰板纸部件尺寸:`, greyResult);
      }

      return results;
    }

    /**
     * 计算单一材料类型的部件尺寸
     */
    private calculateSingleMaterialDimensions(
      part: BoxPart,
      attributes: Record<string, number>,
      materialType: 'face' | 'grey'
    ): PartDimension | null {
      let length = 0;
      let width = 0;
      let hasLengthFormula = false;
      let hasWidthFormula = false;

      const materialTypeName = materialType === 'face' ? '面纸' : '灰板纸';
      perfLog.debug(`计算 ${part.name} 的 ${materialTypeName} 尺寸`);

      // 只处理与当前材料类型相关的公式
      part.formulas?.forEach(formula => {
        if (!formula.expression || !formula.expression.trim()) {
          return;
        }

        const formulaName = formula.name.toLowerCase();
        const isRelevantFormula = this.isFormulaForMaterialType(formulaName, materialType);

        if (!isRelevantFormula) {
          return;
        }

        perfLog.debug(`处理 ${materialTypeName} 公式: ${formula.name} = ${formula.expression}`);

        try {
          const translatedExpression = translateChineseToPinyin(formula.expression);
          const result = evaluate(translatedExpression, attributes);

          perfLog.debug(`${materialTypeName} 公式 ${formula.name} 计算结果: ${result}`);

          if (typeof result === 'number' && result > 0) {
            // 判断是长度还是宽度公式
            if (this.isLengthFormula(formulaName)) {
              length = result;
              hasLengthFormula = true;
              perfLog.debug(`设置 ${materialTypeName} 长度: ${length}`);
            } else if (this.isWidthFormula(formulaName)) {
              width = result;
              hasWidthFormula = true;
              perfLog.debug(`设置 ${materialTypeName} 宽度: ${width}`);
            }
          }
        } catch (error) {
          perfLog.warn(`${materialTypeName} 公式计算错误 ${formula.name}:`, error);
        }
      });

      perfLog.debug(`部件 ${part.name} ${materialTypeName} 计算结果: 长度=${length}, 宽度=${width}, 有长度公式=${hasLengthFormula}, 有宽度公式=${hasWidthFormula}`);

      // 只有同时有长度和宽度公式的材料类型才返回结果
      if (hasLengthFormula && hasWidthFormula && length > 0 && width > 0) {
        return {
          part,
          length,
          width,
          area: length * width,
          canRotate: true,
          isRotated: false,
          materialType // 修复：添加材料类型标识，避免重复显示
        };
      } else {
        perfLog.debug(`跳过部件 ${part.name} 的 ${materialTypeName}: 缺少必要的尺寸信息`);
        return null;
      }
    }

    /**
     * 判断公式是否属于指定材料类型
     */
    private isFormulaForMaterialType(formulaName: string, materialType: 'face' | 'grey'): boolean {
      if (materialType === 'face') {
        return this.containsFacePaperFormulaName(formulaName);
      } else {
        return this.containsGreyBoardFormulaName(formulaName);
      }
    }

    /**
     * 判断是否为长度公式
     */
    private isLengthFormula(formulaName: string): boolean {
      return (formulaName.includes('长度') || formulaName.includes('长')) && !formulaName.includes('宽');
    }

    /**
     * 判断是否为宽度公式
     */
    private isWidthFormula(formulaName: string): boolean {
      return (formulaName.includes('宽度') || formulaName.includes('宽')) && !formulaName.includes('长');
    }

    /**
     * 创建部件组 - 修正：直接使用已按材料类型分离的部件尺寸
     */
    createPartGroups(parts: PartDimension[], materialType: 'face' | 'grey'): PartGroup[] {
      perfLog.debug(`创建 ${materialType} 材料类型的部件组, 输入部件数: ${parts.length}`);

      // 修复：直接使用materialType字段过滤，避免重复显示
      const filteredParts = parts.filter(part => {
        const isMatch = part.materialType === materialType;
        perfLog.debug(`部件 ${part.part.name} 是否匹配 ${materialType} 材料: ${isMatch} (materialType: ${part.materialType})`);
        return isMatch;
      });

      perfLog.debug(`过滤后的 ${materialType} 部件数: ${filteredParts.length}`);

      if (filteredParts.length === 0) {
        perfLog.debug(`没有匹配 ${materialType} 材料的部件`);
        return [];
      }

      // 目前简单实现：每个部件一个组，后续可以添加智能合并逻辑
      // 修复：使用部件ID和索引确保唯一性，避免React key重复
      const groups = filteredParts.map((part, index) => ({
        id: `${materialType}_${part.part.id!.toString()}_${index}`,
        name: `${part.part.name}组`,
        parts: [part],
        totalLength: part.length,
        totalWidth: part.width,
        totalArea: part.area,
        materialType
      }));

      perfLog.debug(`创建的 ${materialType} 部件组:`, groups);
      return groups;
    }

    /**
     * 计算拼版 - 基于打印机尺寸限制
     */
    calculateImposition(partGroup: PartGroup, params: ImpositionParams): ImpositionResult {

      const {
        marginLength,
        marginWidth,
        bleed,
        gripper,
        bite,
        printingMachineMaxLength,
        printingMachineMaxWidth,
        materialMaxLength,
        materialMaxWidth,
        enableMaxImposition = false,
        arrangementMode = 'normal'
      } = params;

      perfLog.debug(`[排列方式计算] 开始计算部件组 ${partGroup.name}:`, {
        arrangementMode,
        enableMaxImposition,
        printingMachineMaxLength,
        printingMachineMaxWidth,
        materialMaxLength,
        materialMaxWidth,
        marginLength,
        marginWidth,
        bleed,
        gripper,
        bite
      });

      // 计算单个成品尺寸（包含出血）
      // 注意：这里的productWidth对应宽度，productHeight对应长度
      const productWidth = partGroup.totalWidth + (bleed * 2);   // 宽度方向
      const productHeight = partGroup.totalLength + (bleed * 2); // 长度方向

      let impositionX = 1; // 横向拼版数量
      let impositionY = 1; // 纵向拼版数量
      let isRotated = false;
      let arrangementDetails: any = undefined;
      
      // 确定实际的尺寸限制（取打印机和材料尺寸的较小值）
      let effectiveMaxLength = undefined;
      let effectiveMaxWidth = undefined;
      let constraintSource = '无约束';

      if (printingMachineMaxLength && printingMachineMaxWidth) {
        effectiveMaxLength = printingMachineMaxLength;
        effectiveMaxWidth = printingMachineMaxWidth;
        constraintSource = '打印机约束';
      }

      if (materialMaxLength && materialMaxWidth) {
        if (effectiveMaxLength && effectiveMaxWidth) {
          // 同时有打印机和材料约束，取较小值
          effectiveMaxLength = Math.min(effectiveMaxLength, materialMaxLength);
          effectiveMaxWidth = Math.min(effectiveMaxWidth, materialMaxWidth);
          constraintSource = '打印机+材料约束';
        } else {
          // 只有材料约束
          effectiveMaxLength = materialMaxLength;
          effectiveMaxWidth = materialMaxWidth;
          constraintSource = '材料约束';
        }
      }

      perfLog.debug(`[尺寸约束] ${partGroup.name} 使用约束:`, {
        constraintSource,
        effectiveMaxLength,
        effectiveMaxWidth,
        printingMachine: { length: printingMachineMaxLength, width: printingMachineMaxWidth },
        material: { length: materialMaxLength, width: materialMaxWidth }
      });

      // 如果有尺寸限制，计算最优拼版
      if (effectiveMaxLength && effectiveMaxWidth && enableMaxImposition) {
        // 计算可用尺寸（减去留边、拉规、咬口）
        const availableLength = effectiveMaxLength - (marginLength * 2) - gripper - bite;
        const availableWidth = effectiveMaxWidth - (marginWidth * 2) - gripper - bite;
        
        // 根据排列方式计算最优拼版
        if (arrangementMode === 'cross') {
          perfLog.debug(`[排列方式] 使用交叉排列模式`);
          // 交叉排列计算
          const crossResult = this.calculateCrossArrangement(
            productWidth,
            productHeight,
            availableWidth,
            availableLength
          );

          // 普通排列计算（用于比较）
          const normalResult = this.calculateNormalArrangement(
            productWidth,
            productHeight,
            availableWidth,
            availableLength
          );

          perfLog.debug(`[排列方式] 交叉排列 vs 普通排列:`, {
            crossResult: {
              totalImposition: crossResult.totalImposition,
              pattern: crossResult.arrangementDetails?.pattern
            },
            normalResult: {
              totalImposition: normalResult.totalImposition,
              pattern: normalResult.arrangementDetails?.pattern
            }
          });
          
          // 选择效率更高的方案
          if (crossResult.totalImposition > normalResult.totalImposition) {
            impositionX = crossResult.impositionX;
            impositionY = crossResult.impositionY;
            isRotated = crossResult.isRotated;
            arrangementDetails = {
              ...crossResult.arrangementDetails,
              userSelectedMode: 'cross',
              actualMode: 'cross',
              decisionReason: `交叉排列拼版数量(${crossResult.totalImposition})优于普通排列(${normalResult.totalImposition})`,
              comparison: {
                normal: {
                  totalImposition: normalResult.totalImposition,
                  pattern: normalResult.arrangementDetails?.pattern || '普通排列'
                },
                cross: {
                  totalImposition: crossResult.totalImposition,
                  pattern: crossResult.arrangementDetails?.pattern || '交叉排列'
                }
              }
            };
            perfLog.debug(`[排列方式] 选择交叉排列方案`);
          } else {
            impositionX = normalResult.impositionX;
            impositionY = normalResult.impositionY;
            isRotated = normalResult.isRotated;
            arrangementDetails = {
              ...normalResult.arrangementDetails,
              userSelectedMode: 'cross',
              actualMode: 'normal',
              decisionReason: crossResult.totalImposition === normalResult.totalImposition
                ? `交叉排列与普通排列效果相同(${normalResult.totalImposition}个/张)，选择更简单的普通排列`
                : `普通排列拼版数量(${normalResult.totalImposition})优于交叉排列(${crossResult.totalImposition})`,
              comparison: {
                normal: {
                  totalImposition: normalResult.totalImposition,
                  pattern: normalResult.arrangementDetails?.pattern || '普通排列'
                },
                cross: {
                  totalImposition: crossResult.totalImposition,
                  pattern: crossResult.arrangementDetails?.pattern || '交叉排列'
                }
              }
            };
            perfLog.debug(`[排列方式] 选择普通排列方案`);
          }
        } else {
          perfLog.debug(`[排列方式] 使用普通排列模式`);
          // 普通排列（不交叉）
          const normalResult = this.calculateNormalArrangement(
            productWidth,
            productHeight,
            availableWidth,
            availableLength
          );

          impositionX = normalResult.impositionX;
          impositionY = normalResult.impositionY;
          isRotated = normalResult.isRotated;
          arrangementDetails = {
            ...normalResult.arrangementDetails,
            userSelectedMode: 'normal',
            actualMode: 'normal',
            decisionReason: '用户选择了普通排列模式'
          };
          perfLog.debug(`[排列方式] 普通排列结果:`, {
            impositionX,
            impositionY,
            totalImposition: impositionX * impositionY,
            pattern: arrangementDetails?.pattern
          });
        }
        
        // 确保至少有一个拼版
        if (impositionX <= 0 || impositionY <= 0) {
          impositionX = 1;
          impositionY = 1;
          isRotated = false;
          arrangementDetails = {
            ...this.createDefaultArrangement(productWidth, productHeight),
            userSelectedMode: arrangementMode as 'normal' | 'cross',
            actualMode: 'normal',
            decisionReason: '打印机尺寸限制，回退到基础排列'
          };
        }
      } else {
        // 没有打印机限制时的默认排列
        arrangementDetails = {
          ...this.createDefaultArrangement(productWidth, productHeight),
          userSelectedMode: arrangementMode as 'normal' | 'cross',
          actualMode: 'normal',
          decisionReason: '未选择打印机，使用默认排列'
        };
      }

      const totalImposition = impositionX * impositionY;

      // 计算实际使用的材料尺寸
      const actualProductWidth = isRotated ? productHeight : productWidth;
      const actualProductHeight = isRotated ? productWidth : productHeight;

      // 普通拼版的材料尺寸 - 修正：gripper和bite不应该重复加到两个方向
      const normalMaterialWidth = (impositionX * actualProductWidth) + (marginWidth * 2) + gripper;
      const normalMaterialLength = (impositionY * actualProductHeight) + (marginLength * 2) + bite;
      const normalMaterialArea = normalMaterialWidth * normalMaterialLength;

      // 初始化最终使用的材料尺寸（默认为普通拼版）
      let finalMaterialWidth = normalMaterialWidth;
      let finalMaterialLength = normalMaterialLength;
      let finalMaterialArea = normalMaterialArea;

      // 计算实际需要的材料张数（基于盒子数量）
      const quantity = params.quantity;

      // 基础材料张数：根据盒子数量和拼版数量计算
      const baseSheetsNeeded = Math.ceil(quantity / totalImposition);

      // 初始化最终材料张数
      let sheetsNeeded = baseSheetsNeeded;

      // 计算需要的材料张数（考虑尺寸限制）
      let actualMaterialArea = finalMaterialArea;

      if (effectiveMaxWidth && effectiveMaxLength) {
        // 检查材料是否超出尺寸限制
        const exceedsWidth = finalMaterialWidth > effectiveMaxWidth;
        const exceedsLength = finalMaterialLength > effectiveMaxLength;

        if (exceedsWidth || exceedsLength) {
          // 如果单张材料超出限制，需要重新计算
          const maxSingleSheetArea = effectiveMaxWidth * effectiveMaxLength;
          const sheetsPerOriginalSheet = Math.ceil(finalMaterialArea / maxSingleSheetArea);
          sheetsNeeded = baseSheetsNeeded * sheetsPerOriginalSheet;

          perfLog.debug(`[材料计算] 材料超出${constraintSource}限制，重新计算张数`, {
            finalMaterialWidth,
            finalMaterialLength,
            effectiveMaxWidth,
            effectiveMaxLength,
            exceedsWidth,
            exceedsLength,
            sheetsPerOriginalSheet,
            originalSheetsNeeded: baseSheetsNeeded,
            finalSheetsNeeded: sheetsNeeded
          });
        }
      }

      perfLog.debug(`[材料张数计算] ${partGroup.name}:`, {
        quantity,
        totalImposition,
        baseSheetsNeeded,
        finalSheetsNeeded: sheetsNeeded
      });

      // 使用最终确定的材料尺寸进行后续计算
      const materialWidth = finalMaterialWidth;
      const materialLength = finalMaterialLength;
      const materialArea = actualMaterialArea;

      // 正确计算拼版后的产品面积
      const singleProductAreaWithBleed = productWidth * productHeight; // 含出血的单个产品面积
      const totalImpositionArea = totalImposition * singleProductAreaWithBleed; // 总拼版面积（所有产品含出血的面积）
      const actualPartArea = totalImposition * partGroup.totalArea; // 实际部件面积（拼版数量 × 单个部件面积）

      // 重新计算正确的利用率 - 修正计算逻辑
      const utilizationAnalysis = {
        // 产品利用率：实际部件面积占含出血产品面积的比例
        productUtilization: singleProductAreaWithBleed > 0 ? (partGroup.totalArea / singleProductAreaWithBleed) * 100 : 0,

        // 拼版利用率：总拼版面积占材料面积的比例（这是主要的利用率指标）
        impositionUtilization: materialArea > 0 ? (totalImpositionArea / materialArea) * 100 : 0,

        // 部件利用率：实际部件面积占材料面积的比例
        partUtilization: materialArea > 0 ? (actualPartArea / materialArea) * 100 : 0,

        // 原始计算（保留兼容性）- 修正：应该使用拼版利用率作为主要指标
        originalCalculation: materialArea > 0 ? (totalImpositionArea / materialArea) * 100 : 0,
        
        // 详细分解
        breakdown: {
          partArea: partGroup.totalArea,                        // 单个部件面积（不含出血）
          singleProductAreaWithBleed: singleProductAreaWithBleed, // 单个产品面积（含出血）
          totalImpositionArea: totalImpositionArea,             // 总拼版面积
          actualPartArea: actualPartArea,                       // 实际部件总面积
          materialArea: materialArea,                           // 材料总面积
          wasteArea: materialArea - totalImpositionArea,        // 浪费面积
          impositionCount: totalImposition,                     // 拼版数量
          sheetsNeeded: sheetsNeeded,                          // 需要的材料张数
          // 保留兼容性字段
          productAreaWithBleed: singleProductAreaWithBleed,
          totalProductArea: totalImpositionArea
        }
      };

      perfLog.debug('修正后的利用率计算:', {
        partGroup: partGroup.name,
        totalImposition,
        拼版利用率: utilizationAnalysis.impositionUtilization.toFixed(2) + '%',
        部件利用率: utilizationAnalysis.partUtilization.toFixed(2) + '%',
        产品利用率: utilizationAnalysis.productUtilization.toFixed(2) + '%',
        材料张数: sheetsNeeded,
        计算详情: utilizationAnalysis.breakdown
      });

      // 最终排列方式结果日志
      perfLog.debug(`[排列方式] ${partGroup.name} 最终结果:`, {
        arrangementMode: params.arrangementMode,
        impositionX,
        impositionY,
        totalImposition,
        isRotated,
        arrangementPattern: arrangementDetails?.pattern,
        efficiency: utilizationAnalysis.impositionUtilization.toFixed(2) + '%'
      });

      const warnings: string[] = [];
      if (utilizationAnalysis.impositionUtilization < 60) {
        warnings.push('材料利用率较低，建议优化拼版方案');
      }
      
      // 添加利用率合理性检查
      if (utilizationAnalysis.impositionUtilization > 100) {
        perfLog.warn('警告：利用率计算结果超过100%，可能存在计算错误');
        warnings.push('利用率计算异常，请检查参数设置');
      }

      const result: ImpositionResult = {
        materialType: partGroup.materialType,
        partGroup,
        impositionX,
        impositionY,
        totalImposition,
        materialLength,
        materialWidth,
        materialArea,
        efficiency: utilizationAnalysis.impositionUtilization,
        sheetsNeeded: sheetsNeeded, // 使用计算的材料张数
        wasteArea: utilizationAnalysis.breakdown.wasteArea,
        warnings,
        // 新增字段 - 修正：返回实际使用的产品尺寸（考虑旋转）
        productWidth: actualProductWidth,   // 实际使用的产品宽度
        productHeight: actualProductHeight, // 实际使用的产品长度
        isRotated,
        constraintUsage: effectiveMaxLength && effectiveMaxWidth ? {
          constraintSource,
          maxLength: effectiveMaxLength,
          maxWidth: effectiveMaxWidth,
          usedLength: materialLength,
          usedWidth: materialWidth,
          lengthUtilization: (materialLength / effectiveMaxLength) * 100,
          widthUtilization: (materialWidth / effectiveMaxWidth) * 100,
          printingMachine: printingMachineMaxLength && printingMachineMaxWidth ? {
            maxLength: printingMachineMaxLength,
            maxWidth: printingMachineMaxWidth
          } : undefined,
          material: materialMaxLength && materialMaxWidth ? {
            maxLength: materialMaxLength,
            maxWidth: materialMaxWidth
          } : undefined
        } : undefined,
        arrangementDetails,
        utilizationAnalysis
      };

      return result;
    }

    /**
     * 优化拼版
     */
    optimizeImposition(
      partGroups: PartGroup[],
      params: ImpositionParams,
      options: ImpositionOptimization,
      basicInfo?: any
    ): ImpositionResult[] {
      const results: ImpositionResult[] = [];

      // 基础拼版计算 - 为每个部件组使用对应的材料约束
      partGroups.forEach(group => {
        // 获取该部件组的材料约束
        const materialConstraints = params.getMaterialConstraints ? params.getMaterialConstraints(group.id) : null;

        perfLog.debug(`获取部件组 ${group.id} (${group.name}) 的材料约束:`, {
          hasGetMaterialConstraints: !!params.getMaterialConstraints,
          materialConstraints,
          groupId: group.id,
          groupName: group.name
        });

        // 创建该部件组专用的参数
        const groupParams = {
          ...params,
          printingMachineMaxLength: materialConstraints?.printingMachineMaxLength || undefined,
          printingMachineMaxWidth: materialConstraints?.printingMachineMaxWidth || undefined,
          materialMaxLength: materialConstraints?.materialSize?.height || undefined,
          materialMaxWidth: materialConstraints?.materialSize?.width || undefined
        };

        perfLog.debug(`计算部件组 ${group.name} 的拼版，使用约束:`, {
          printingMachineMaxLength: groupParams.printingMachineMaxLength,
          printingMachineMaxWidth: groupParams.printingMachineMaxWidth,
          materialMaxLength: groupParams.materialMaxLength,
          materialMaxWidth: groupParams.materialMaxWidth,
          materialSize: materialConstraints?.materialSize,
          materialName: materialConstraints?.materialName,
          materialSpec: materialConstraints?.materialSpec
        });

        const baseResult = this.calculateImposition(group, groupParams);
        results.push(baseResult);
      });

      // 如果启用了智能优化，使用高级算法
      if (options.enableGroupMerge || options.enableDynamicGrouping) {
        perfLog.debug('启用智能拼版优化...');
        try {
          const optimizedScheme = this.intelligentOptimizeImposition(partGroups, params, options, basicInfo);
          return optimizedScheme.impositionResults;
        } catch (error) {
          perfLog.error('智能优化失败，使用基础算法:', error);
        }
      }

      // 基础优化：仅排序
      switch (options.optimizeFor) {
        case 'efficiency':
          results.sort((a, b) => b.efficiency - a.efficiency);
          break;
        case 'cost':
          results.sort((a, b) => a.materialArea - b.materialArea);
          break;
        case 'time':
          results.sort((a, b) => a.totalImposition - b.totalImposition);
          break;
      }

      return results;
    }

    /**
     * 智能拼版优化（新增方法）
     */
    intelligentOptimizeImposition(
      partGroups: PartGroup[],
      params: ImpositionParams,
      options: ImpositionOptimization,
      _basicInfo?: any
    ): OptimizationScheme {
      const startTime = Date.now();
      perfLog.debug(`开始智能拼版优化，部件组数: ${partGroups.length}`);

      // 创建基准方案
      const baselineScheme = this.createBaselineScheme(partGroups, params, options);

      let bestScheme = baselineScheme;
      let iteration = 0;

      // 迭代优化
      while (iteration < options.maxIterations && !this.hasConverged(bestScheme, options)) {
        iteration++;
        perfLog.debug(`优化迭代 ${iteration}/${options.maxIterations}`);

        // 1. 尝试部件合并优化
        if (options.enableGroupMerge) {
          const mergedScheme = this.tryGroupMerging(bestScheme, params, options);
          if (mergedScheme && mergedScheme.totalScore > bestScheme.totalScore) {
            bestScheme = mergedScheme;
            perfLog.debug(`合并优化成功，评分提升: ${bestScheme.totalScore.toFixed(2)}`);
          }
        }

        // 2. 尝试旋转优化
        if (options.enableRotation || options.enableMixedRotation) {
          const rotatedScheme = this.tryRotationOptimization(bestScheme, params, options);
          if (rotatedScheme && rotatedScheme.totalScore > bestScheme.totalScore) {
            bestScheme = rotatedScheme;
            perfLog.debug(`旋转优化成功，评分提升: ${bestScheme.totalScore.toFixed(2)}`);
          }
        }

        // 3. 尝试动态重组
        if (options.enableDynamicGrouping) {
          const regroupedScheme = this.tryDynamicRegrouping(bestScheme, params, options);
          if (regroupedScheme && regroupedScheme.totalScore > bestScheme.totalScore) {
            bestScheme = regroupedScheme;
            perfLog.debug(`重组优化成功，评分提升: ${bestScheme.totalScore.toFixed(2)}`);
          }
        }
      }

      const processingTime = Date.now() - startTime;
      perfLog.info(`优化完成，用时: ${processingTime}ms，最终评分: ${bestScheme.totalScore.toFixed(2)}`);

      // 更新处理时间和迭代信息
      bestScheme.metrics.estimatedTime = processingTime;

      return bestScheme;
    }

    /**
     * 创建基准方案
     */
    private createBaselineScheme(
      partGroups: PartGroup[],
      params: ImpositionParams,
      options: ImpositionOptimization
    ): OptimizationScheme {
      const impositionResults = partGroups.map(group => {
        // 获取该部件组的材料约束
        const materialConstraints = params.getMaterialConstraints ? params.getMaterialConstraints(group.id) : null;

        // 创建该部件组专用的参数
        const groupParams = {
          ...params,
          printingMachineMaxLength: materialConstraints?.printingMachineMaxLength || undefined,
          printingMachineMaxWidth: materialConstraints?.printingMachineMaxWidth || undefined,
          materialMaxLength: materialConstraints?.materialSize?.height || undefined,
          materialMaxWidth: materialConstraints?.materialSize?.width || undefined
        };

        return this.calculateImposition(group, groupParams);
      });

      const scheme: OptimizationScheme = {
        id: `baseline_${Date.now()}`,
        name: '基准方案',
        partGroups: [...partGroups],
        impositionResults,
        totalScore: 0,
        metrics: {
          totalEfficiency: this.calculateAverageEfficiency(impositionResults),
          totalMaterialArea: impositionResults.reduce((sum, r) => sum + r.materialArea, 0),
          totalImposition: impositionResults.reduce((sum, r) => sum + r.totalImposition, 0),
          groupCount: partGroups.length,
          rotationCount: impositionResults.filter(r => r.isRotated).length,
          estimatedCost: 0,
          estimatedTime: 0
        },
        details: {
          mergedParts: [],
        }
      };

      scheme.totalScore = this.evaluateOptimizationScheme(scheme, options);
      return scheme;
    }

    /**
     * 尝试部件合并优化
     */
    private tryGroupMerging(
      currentScheme: OptimizationScheme,
      params: ImpositionParams,
      options: ImpositionOptimization
    ): OptimizationScheme | null {
      perfLog.debug('尝试部件合并优化...');

      const mergeCandidates = this.generateMergeCandidates(
        currentScheme.partGroups,
        params,
        options
      );

      if (mergeCandidates.length === 0) {
        perfLog.debug('没有找到可合并的部件组');
        return null;
      }

      // 选择最佳合并候选项
      const bestCandidate = mergeCandidates
        .filter(c => c.feasible)
        .sort((a, b) => b.efficiencyImprovement - a.efficiencyImprovement)[0];

      if (!bestCandidate) {
        perfLog.debug('没有找到可行的合并方案');
        return null;
      }

      // 创建合并后的方案
      const newPartGroups = [...currentScheme.partGroups];
      
      // 移除原始组
      bestCandidate.groups.forEach(group => {
        const index = newPartGroups.findIndex(g => g.id === group.id);
        if (index !== -1) {
          newPartGroups.splice(index, 1);
        }
      });

      // 添加合并后的组
      newPartGroups.push(bestCandidate.combinedGroup);

      // 重新计算拼版 - 使用各自的材料约束
      const newImpositionResults = newPartGroups.map(group => {
        const materialConstraints = params.getMaterialConstraints ? params.getMaterialConstraints(group.id) : null;
        const groupParams = {
          ...params,
          printingMachineMaxLength: materialConstraints?.printingMachineMaxLength || undefined,
          printingMachineMaxWidth: materialConstraints?.printingMachineMaxWidth || undefined,
          materialMaxLength: materialConstraints?.materialSize?.height || undefined,
          materialMaxWidth: materialConstraints?.materialSize?.width || undefined
        };
        return this.calculateImposition(group, groupParams);
      });

      const newScheme: OptimizationScheme = {
        id: `merged_${Date.now()}`,
        name: '合并优化方案',
        partGroups: newPartGroups,
        impositionResults: newImpositionResults,
        totalScore: 0,
        metrics: {
          totalEfficiency: this.calculateAverageEfficiency(newImpositionResults),
          totalMaterialArea: newImpositionResults.reduce((sum, r) => sum + r.materialArea, 0),
          totalImposition: newImpositionResults.reduce((sum, r) => sum + r.totalImposition, 0),
          groupCount: newPartGroups.length,
          rotationCount: newImpositionResults.filter(r => r.isRotated).length,
          estimatedCost: 0,
          estimatedTime: 0
        },
        details: {
          mergedParts: [{
            originalGroups: bestCandidate.groups.map(g => g.id),
            newGroup: bestCandidate.combinedGroup,
            improvementRatio: bestCandidate.efficiencyImprovement
          }]
        }
      };

      newScheme.totalScore = this.evaluateOptimizationScheme(newScheme, options);

      perfLog.debug(`合并评估完成，效率改进: ${bestCandidate.efficiencyImprovement.toFixed(2)}%`);
      return newScheme;
    }

    /**
     * 尝试旋转优化
     */
    private tryRotationOptimization(
      _currentScheme: OptimizationScheme,
      _params: ImpositionParams,
      _options: ImpositionOptimization
    ): OptimizationScheme | null {
      perfLog.debug('尝试旋转优化...');

      // 这里可以实现更复杂的旋转优化逻辑
      // 目前旋转已经在calculateImposition中处理，这里返回null表示没有额外改进
      return null;
    }

    /**
     * 尝试动态重组
     */
    private tryDynamicRegrouping(
      _currentScheme: OptimizationScheme,
      _params: ImpositionParams,
      _options: ImpositionOptimization
    ): OptimizationScheme | null {
      perfLog.debug('尝试动态重组...');

      // 这里可以实现动态重组逻辑
      // 目前返回null表示没有改进
      return null;
    }

    /**
     * 检查是否已收敛
     */
    private hasConverged(scheme: OptimizationScheme, _options: ImpositionOptimization): boolean {
      // 简单的收敛检查：如果效率已经很高，则认为收敛
      return scheme.metrics.totalEfficiency >= 95.0;
    }

    /**
     * 计算平均效率
     */
    private calculateAverageEfficiency(results: ImpositionResult[]): number {
      if (results.length === 0) return 0;
      const totalEfficiency = results.reduce((sum, r) => sum + r.efficiency, 0);
      return totalEfficiency / results.length;
    }

    /**
     * 生成合并候选项
     */
    generateMergeCandidates(groups: PartGroup[], params: ImpositionParams, options: ImpositionOptimization, _basicInfo?: any): MergeCandidate[] {
      perfLog.debug(`生成合并候选项，组数: ${groups.length}`);

      const candidates: MergeCandidate[] = [];

      // 尝试两两合并
      for (let i = 0; i < groups.length; i++) {
        for (let j = i + 1; j < groups.length; j++) {
          const group1 = groups[i];
          const group2 = groups[j];

          // 检查是否同材料类型
          if (group1.materialType !== group2.materialType) continue;

          // 检查部件数量限制
          const totalParts = group1.parts.length + group2.parts.length;
          if (totalParts > options.mergeStrategy.maxPartsPerGroup) continue;

          // 创建合并组 - 修正：使用加法计算合并尺寸
          const combinedGroup: PartGroup = {
            id: `merged_${group1.id}_${group2.id}`,
            name: `${group1.name}+${group2.name}`,
            parts: [...group1.parts, ...group2.parts],
            // 横向排列：宽度相加，长度取最大值
            totalWidth: group1.totalWidth + group2.totalWidth,
            totalLength: Math.max(group1.totalLength, group2.totalLength),
            totalArea: group1.totalArea + group2.totalArea,
            materialType: group1.materialType
          };

          // 计算效率改进 - 使用各自的材料约束
          const constraints1 = params.getMaterialConstraints ? params.getMaterialConstraints(group1.id) : null;
          const constraints2 = params.getMaterialConstraints ? params.getMaterialConstraints(group2.id) : null;
          const constraintsCombined = params.getMaterialConstraints ? params.getMaterialConstraints(combinedGroup.id) : null;

          const groupParams1 = {
            ...params,
            printingMachineMaxLength: constraints1?.printingMachineMaxLength || undefined,
            printingMachineMaxWidth: constraints1?.printingMachineMaxWidth || undefined,
            materialMaxLength: constraints1?.materialSize?.height || undefined,
            materialMaxWidth: constraints1?.materialSize?.width || undefined
          };

          const groupParams2 = {
            ...params,
            printingMachineMaxLength: constraints2?.printingMachineMaxLength || undefined,
            printingMachineMaxWidth: constraints2?.printingMachineMaxWidth || undefined,
            materialMaxLength: constraints2?.materialSize?.height || undefined,
            materialMaxWidth: constraints2?.materialSize?.width || undefined
          };

          const groupParamsCombined = {
            ...params,
            printingMachineMaxLength: constraintsCombined?.printingMachineMaxLength || undefined,
            printingMachineMaxWidth: constraintsCombined?.printingMachineMaxWidth || undefined,
            materialMaxLength: constraintsCombined?.materialSize?.height || undefined,
            materialMaxWidth: constraintsCombined?.materialSize?.width || undefined
          };

          const originalResult1 = this.calculateImposition(group1, groupParams1);
          const originalResult2 = this.calculateImposition(group2, groupParams2);
          const combinedResult = this.calculateImposition(combinedGroup, groupParamsCombined);

          const originalTotalArea = originalResult1.materialArea + originalResult2.materialArea;
          const efficiencyImprovement = originalTotalArea > 0 
            ? ((originalTotalArea - combinedResult.materialArea) / originalTotalArea) * 100 
            : 0;

          const candidate: MergeCandidate = {
            groups: [group1, group2],
            combinedGroup,
            efficiencyImprovement,
            materialSaving: originalTotalArea - combinedResult.materialArea,
            complexity: totalParts, // 简单的复杂度评估
            feasible: efficiencyImprovement > 0 && combinedResult.efficiency >= options.mergeStrategy.minGroupEfficiency
          };

          candidates.push(candidate);
        }
      }

      perfLog.debug(`生成了 ${candidates.length} 个合并候选项`);
      return candidates.filter(c => c.feasible);
    }

    /**
     * 评估优化方案
     */
    evaluateOptimizationScheme(scheme: OptimizationScheme, options: ImpositionOptimization): number {
      const weights = options.scoreWeights;
      
      // 归一化分数计算
      const efficiencyScore = scheme.metrics.totalEfficiency / 100; // 0-1
      const impositionScore = Math.min(scheme.metrics.totalImposition / 100, 1); // 0-1，超过100个拼版数认为满分
      const materialScore = 1 / (1 + scheme.metrics.totalMaterialArea / 1000000); // 材料面积越小越好，归一化
      const groupScore = 1 / (1 + scheme.metrics.groupCount / 10); // 组数越少越好
      const rotationScore = 1 - (scheme.metrics.rotationCount * weights.rotationPenalty / scheme.partGroups.length); // 旋转惩罚

      const totalScore = 
        efficiencyScore * weights.efficiency +
        impositionScore * weights.totalImposition +
        materialScore * weights.materialArea +
        groupScore * weights.groupCount +
        Math.max(0, rotationScore); // 确保不为负

      perfLog.debug(`方案评分 - 效率:${efficiencyScore.toFixed(2)} 拼版:${impositionScore.toFixed(2)} 材料:${materialScore.toFixed(2)} 分组:${groupScore.toFixed(2)} 旋转:${rotationScore.toFixed(2)} 总分:${totalScore.toFixed(2)}`);

      return totalScore;
    }

    /**
     * 检查公式名称是否包含面纸相关内容
     */
    private containsFacePaperFormulaName(formulaName: string): boolean {
      const faceKeywords = ['面纸长度', '面纸宽度', '面纸长', '面纸宽', 'face_length', 'face_width'];
      return faceKeywords.some(keyword => formulaName.includes(keyword.toLowerCase()));
    }

    /**
     * 检查公式名称是否包含灰板纸相关内容
     */
    private containsGreyBoardFormulaName(formulaName: string): boolean {
      const greyKeywords = ['灰板纸长度', '灰板纸宽度', '灰板长度', '灰板宽度', '灰板长', '灰板宽', 'grey_length', 'grey_width'];
      return greyKeywords.some(keyword => formulaName.includes(keyword.toLowerCase()));
    }

    // 注意：isPartForMaterial 方法已移除，现在直接使用 PartDimension.materialType 字段进行过滤

    /**
     * 计算普通排列（不交叉）
     */
    private calculateNormalArrangement(
      productWidth: number, 
      productHeight: number, 
      availableWidth: number, 
      availableLength: number
    ) {
      // 计算基本拼版数量（不旋转）
      const normalImpositionX = Math.floor(availableWidth / productWidth);
      const normalImpositionY = Math.floor(availableLength / productHeight);
      const normalTotal = normalImpositionX * normalImpositionY;
      
      // 计算旋转后的拼版数量
      const rotatedImpositionX = Math.floor(availableWidth / productHeight);
      const rotatedImpositionY = Math.floor(availableLength / productWidth);
      const rotatedTotal = rotatedImpositionX * rotatedImpositionY;
      
      let finalX, finalY, isRotated;
      
      // 选择拼版数量更多的方案
      if (rotatedTotal > normalTotal) {
        finalX = rotatedImpositionX;
        finalY = rotatedImpositionY;
        isRotated = true;
      } else {
        finalX = normalImpositionX;
        finalY = normalImpositionY;
        isRotated = false;
      }

      // 创建排列详情
      const arrangementDetails = {
        mode: 'normal' as const,
        actualMode: 'normal' as const,
        pattern: isRotated ? '普通排列（已旋转）' : '普通排列',
        items: this.generateNormalArrangementItems(finalX, finalY, productWidth, productHeight, isRotated),
        efficiencyGain: 0 // 普通排列作为基准，增益为0
      };

      return {
        impositionX: finalX,
        impositionY: finalY,
        totalImposition: finalX * finalY,
        isRotated,
        arrangementDetails
      };
    }

    /**
     * 计算交叉排列
     */
    private calculateCrossArrangement(
      productWidth: number, 
      productHeight: number, 
      availableWidth: number, 
      availableLength: number
    ) {
      perfLog.debug('计算交叉排列:', { productWidth, productHeight, availableWidth, availableLength });
      
      // 首先尝试棋盘式交叉排列（相邻产品旋转180度）
      const chessboardResult = this.calculateChessboardArrangement(productWidth, productHeight, availableWidth, availableLength);
      
      // 然后尝试行交叉排列（奇偶行旋转）
      const rowCrossResult = this.calculateRowCrossArrangement(productWidth, productHeight, availableWidth, availableLength);
      
      // 选择更优的交叉方案
      if (chessboardResult.totalImposition >= rowCrossResult.totalImposition) {
        return chessboardResult;
      } else {
        return rowCrossResult;
      }
    }

    /**
     * 计算棋盘式交叉排列
     */
    private calculateChessboardArrangement(
      productWidth: number, 
      productHeight: number, 
      availableWidth: number, 
      availableLength: number
    ) {
      // 对于棋盘式排列，相邻的产品可能会有180度旋转
      // 这种方式特别适用于长方形产品
      
      // 简化算法：假设可以在一定程度上节省空间
      const spaceSavingFactor = 0.9; // 假设交叉排列可以节省10%的空间
      
      const effectiveProductWidth = Math.min(productWidth, productHeight) * spaceSavingFactor + 
                                   Math.max(productWidth, productHeight) * (1 - spaceSavingFactor);
      const effectiveProductHeight = Math.min(productWidth, productHeight) * spaceSavingFactor + 
                                    Math.max(productWidth, productHeight) * (1 - spaceSavingFactor);
      
      const impositionX = Math.floor(availableWidth / effectiveProductWidth);
      const impositionY = Math.floor(availableLength / effectiveProductHeight);
      const totalImposition = impositionX * impositionY;
      
      // 创建棋盘式排列的详细布局
      const items = this.generateChessboardArrangementItems(
        impositionX, 
        impositionY, 
        productWidth, 
        productHeight
      );

      const arrangementDetails = {
        mode: 'cross' as const,
        actualMode: 'cross' as const,
        pattern: '棋盘式交叉排列',
        items,
        efficiencyGain: 0 // 稍后计算
      };

      return {
        impositionX,
        impositionY,
        totalImposition,
        isRotated: false, // 混合旋转，不是整体旋转
        arrangementDetails
      };
    }

    /**
     * 计算行交叉排列
     */
    private calculateRowCrossArrangement(
      productWidth: number, 
      productHeight: number, 
      availableWidth: number, 
      availableLength: number
    ) {
      // 行交叉排列：奇数行正常，偶数行旋转180度
      // 这种方式在某些情况下可以更好地利用空间
      
      const normalRowWidth = productWidth;
      const normalRowHeight = productHeight;
      const rotatedRowWidth = productHeight;
      const rotatedRowHeight = productWidth;
      
      // 计算一行能放多少个
      const itemsPerNormalRow = Math.floor(availableWidth / normalRowWidth);
      const itemsPerRotatedRow = Math.floor(availableWidth / rotatedRowWidth);
      
      // 计算总行数（交替排列）
      const totalRowHeight = normalRowHeight + rotatedRowHeight;
      const rowPairs = Math.floor(availableLength / totalRowHeight);
      
      const totalImposition = rowPairs * (itemsPerNormalRow + itemsPerRotatedRow);
      
      // 为了简化，这里用等效的impositionX和impositionY表示
      const avgItemsPerRow = (itemsPerNormalRow + itemsPerRotatedRow) / 2;
      const effectiveRows = rowPairs * 2;
      
      const items = this.generateRowCrossArrangementItems(
        itemsPerNormalRow,
        itemsPerRotatedRow,
        rowPairs,
        productWidth,
        productHeight
      );

      const arrangementDetails = {
        mode: 'cross' as const,
        actualMode: 'cross' as const,
        pattern: '行交叉排列',
        items,
        efficiencyGain: 0 // 稍后计算
      };

      return {
        impositionX: Math.ceil(avgItemsPerRow),
        impositionY: effectiveRows,
        totalImposition,
        isRotated: false, // 混合旋转
        arrangementDetails
      };
    }

    /**
     * 生成普通排列的项目布局
     */
    private generateNormalArrangementItems(
      cols: number, 
      rows: number, 
      productWidth: number, 
      productHeight: number, 
      isRotated: boolean
    ) {
      const items = [];
      const itemWidth = isRotated ? productHeight : productWidth;
      const itemHeight = isRotated ? productWidth : productHeight;
      
      for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
          items.push({
            x: col * itemWidth,
            y: row * itemHeight,
            width: itemWidth,
            height: itemHeight,
            rotation: isRotated ? 90 : 0,
            index: row * cols + col
          });
        }
      }
      
      return items;
    }

    /**
     * 生成棋盘式排列的项目布局
     */
    private generateChessboardArrangementItems(
      cols: number, 
      rows: number, 
      productWidth: number, 
      productHeight: number
    ) {
      const items = [];
      
      for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
          // 棋盘式：根据行列位置决定是否旋转
          const shouldRotate = (row + col) % 2 === 1;
          const itemWidth = shouldRotate ? productHeight : productWidth;
          const itemHeight = shouldRotate ? productWidth : productHeight;
          
          items.push({
            x: col * Math.max(productWidth, productHeight),
            y: row * Math.max(productWidth, productHeight),
            width: itemWidth,
            height: itemHeight,
            rotation: shouldRotate ? 180 : 0,
            index: row * cols + col
          });
        }
      }
      
      return items;
    }

    /**
     * 生成行交叉排列的项目布局
     */
    private generateRowCrossArrangementItems(
      normalRowItems: number,
      rotatedRowItems: number, 
      rowPairs: number,
      productWidth: number, 
      productHeight: number
    ) {
      const items = [];
      let itemIndex = 0;
      
      for (let pair = 0; pair < rowPairs; pair++) {
        const baseY = pair * (productHeight + productWidth);
        
        // 正常行
        for (let col = 0; col < normalRowItems; col++) {
          items.push({
            x: col * productWidth,
            y: baseY,
            width: productWidth,
            height: productHeight,
            rotation: 0,
            index: itemIndex++
          });
        }
        
        // 旋转行
        for (let col = 0; col < rotatedRowItems; col++) {
          items.push({
            x: col * productHeight,
            y: baseY + productHeight,
            width: productHeight,
            height: productWidth,
            rotation: 180,
            index: itemIndex++
          });
        }
      }
      
      return items;
    }

    /**
     * 创建默认排列（无打印机限制时）
     */
    private createDefaultArrangement(productWidth: number, productHeight: number) {
      return {
        mode: 'normal' as const,
        actualMode: 'normal' as const,
        pattern: '默认排列（1×1）',
        items: [{
          x: 0,
          y: 0,
          width: productWidth,
          height: productHeight,
          rotation: 0,
          index: 0
        }],
        efficiencyGain: 0
      };
    }

    // 双接相关方法已移除
}

// 创建单例实例
export const packagingEngine = new PackagingCalculationEngineImpl(); 