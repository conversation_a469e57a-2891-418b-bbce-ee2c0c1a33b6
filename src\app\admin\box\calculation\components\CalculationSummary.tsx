'use client';

import React, { useEffect, useRef } from 'react';
import { Card, Statistic, Button, Space, Divider, Typography, Tooltip } from 'antd';
import { CalculatorOutlined, ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { QuotationDetail, BoxBasicInfo } from '../types/calculation';
import { getSpecDisplayName, getChineseUnit, formatCurrency } from '../util';

const { Text } = Typography;

interface CalculationSummaryProps {
  quotation: QuotationDetail;
  basicInfo: BoxBasicInfo;
  isCalculating: boolean;
  onRecalculate: () => void;
  // 新增：监听拼版计算状态变化
  hasCalculatedImposition?: boolean;
  // 新增：材料成本详情，用于悬浮提示
  materialCostDetails?: any[];
}

/**
 * 费用汇总组件
 */
const CalculationSummary: React.FC<CalculationSummaryProps> = ({
  quotation,
  basicInfo,
  isCalculating,
  onRecalculate,
  hasCalculatedImposition = false,
  materialCostDetails = []
}) => {
  // 用于跟踪上一次的拼版计算状态和材料费用
  const prevImpositionStateRef = useRef<boolean>(false);
  const prevMaterialCostRef = useRef<number>(0);
  const prevMaterialDetailsCountRef = useRef<number>(0);

  // 监听拼版计算状态变化和材料费用变化，自动触发费用重新计算
  useEffect(() => {
    const currentMaterialCost = quotation.materialCost || 0;
    const currentDetailsCount = materialCostDetails?.length || 0;

    // 检查是否需要触发重新计算
    const shouldRecalculate = (
      // 拼版计算从未完成变为完成
      (hasCalculatedImposition && !prevImpositionStateRef.current) ||
      // 材料费用发生变化
      (currentMaterialCost !== prevMaterialCostRef.current) ||
      // 材料详情数量发生变化
      (currentDetailsCount !== prevMaterialDetailsCountRef.current && currentDetailsCount > 0)
    );

    if (shouldRecalculate) {
      console.log('检测到状态变化，自动触发费用重新计算:', {
        hasCalculatedImposition,
        prevImposition: prevImpositionStateRef.current,
        currentMaterialCost,
        prevMaterialCost: prevMaterialCostRef.current,
        currentDetailsCount,
        prevDetailsCount: prevMaterialDetailsCountRef.current
      });

      // 延迟一小段时间确保状态已完全更新
      setTimeout(() => {
        onRecalculate();
      }, 200);
    }

    // 更新引用值
    prevImpositionStateRef.current = hasCalculatedImposition;
    prevMaterialCostRef.current = currentMaterialCost;
    prevMaterialDetailsCountRef.current = currentDetailsCount;
  }, [hasCalculatedImposition, quotation.materialCost, materialCostDetails, onRecalculate]);

  const currentQuantity = basicInfo.quantity || 0;

  // 生成材料费用悬浮提示内容
  const getMaterialCostTooltip = () => {
    if (!materialCostDetails || materialCostDetails.length === 0) {
      return '暂无材料费用详情';
    }

    return (
      <div className="max-w-xs">
        <div className="font-medium mb-2">材料费用详情：</div>
        {materialCostDetails.map((detail: any, index: number) => (
          <div key={index} className="mb-1 text-xs">
            <div className="font-medium">{detail.materialName}</div>
            <div>规格：{getSpecDisplayName(detail.specification || '')}</div>
            <div>数量：{detail.quantity?.toFixed(2)} {getChineseUnit(detail.unit || '张')}</div>
            <div>单价：¥{detail.unitPrice?.toFixed(2)}/{getChineseUnit(detail.unit || '张')}</div>
            <div>小计：¥{detail.totalCost?.toFixed(2)}</div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <CalculatorOutlined />
          <span>费用汇总</span>
        </Space>
      }
      extra={
        <Button
          icon={<ReloadOutlined />}
          onClick={onRecalculate}
          loading={isCalculating}
        >
          重算
        </Button>
      }
      className="shadow-lg"
    >
      {/* 基础信息 */}
      <div>
        <Text type="secondary" className="text-xs">数量</Text>
        <div className="font-medium text-sm">{currentQuantity} 个</div>
      </div>

      <Divider className="my-2" />

      {/* 费用明细 */}
      <Space direction="vertical" className="w-full" size="small">
        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title={getMaterialCostTooltip()} placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>材料费用</Text>
            <Text>{formatCurrency(quotation.materialCost)}</Text>
          </Space>
        </div>

        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="包含印刷、覆膜、丝印、烫金、凹凸、模切等工艺费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>工艺费用</Text>
            <Text>{formatCurrency(quotation.processCost)}</Text>
          </Space>
        </div>

        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="包含常规配件和礼盒配件费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>配件费用</Text>
            <Text>{formatCurrency(quotation.accessoryCost)}</Text>
          </Space>
        </div>

        <div className="flex justify-between items-center">
          <Space>
            <Tooltip title="自定义的额外费用" placement="topLeft">
              <InfoCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
            <Text style={{ marginRight: 10, fontWeight: 'bold' }}>自定义费用</Text>
            <Text>{formatCurrency(quotation.formulaCost)}</Text>
          </Space>
        </div>
      </Space>

      <Divider className="my-2" />

      {/* 总价 */}
      <div className="bg-blue-50 p-3 rounded-lg">
        <Statistic
          title="总费用"
          value={quotation.totalCost}
          formatter={(value) => formatCurrency(Number(value))}
          valueStyle={{ color: '#1890ff', fontSize: '18px', fontWeight: 'bold' }}
        />
      </div>

      {/* 单价 */}
      {currentQuantity > 0 && (
        <div className="bg-gray-50 p-2 rounded text-center">
          <Text type="secondary" className="text-xs">单价</Text>
          <div className="text-sm font-semibold text-gray-700">
            {formatCurrency(quotation.totalCost / currentQuantity)}
          </div>
        </div>
      )}
    </Card>
  );
};

export default CalculationSummary; 