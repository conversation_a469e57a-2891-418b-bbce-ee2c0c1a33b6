'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Collapse,
  Descriptions,
  Tag,
  Modal,
  Form,
  InputNumber,
  Alert,
  Tooltip,
  Row,
  Col,
  Divider,
  message
} from 'antd';
import {
  DownloadOutlined,
  PrinterOutlined,
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  CopyOutlined,
  FormOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { CalculationState, FormulaConfig } from '../../types/calculation';
import type { MaterialCostDetail } from '../../types/calculation';
import { CustomFormula, FormulaStatus } from '@/types/customFormula';
import { customFormulaApi } from '@/services/adminApi';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import { perfLog } from '@/lib/utils/perfLog';
import { evaluate } from 'mathjs';
import { translateChineseToPinyin } from '@/lib/utils/formula';
import { getSpecDisplayName, getChineseUnit} from '../../util';
import {
  exportQuotationToPDFFromHTML,
  generateDetailedQuotationHTML,
  executePrintQuotation,
  formatMaterialSize,
  formatMaterialSpec,
  formatMaterialType,
  getPartGroupDisplayName
} from '../../utils/pdfExport';
import { formatCurrency } from '../../util';

const { Title, Text } = Typography;

interface QuotationStepProps {
  state: CalculationState;
  onUpdate: {
    formulaConfig: (data: Partial<FormulaConfig>) => void;
  };
  onRecalculate: () => void;
}

// 导入公式计算结果类型
import type { FormulaCalculationResult } from '../../types/calculation';

const QuotationStep: React.FC<QuotationStepProps> = ({ state, onUpdate, onRecalculate }) => {
  const { execute } = useAsyncError();

  // 自定义公式相关状态
  const [availableFormulas, setAvailableFormulas] = useState<CustomFormula[]>([]);
  const [formulaModalVisible, setFormulaModalVisible] = useState(false);
  const [selectedFormula, setSelectedFormula] = useState<CustomFormula | null>(null);
  const [formulaInputModalVisible, setFormulaInputModalVisible] = useState(false);
  const [formulaForm] = Form.useForm();
  // 使用全局状态中的公式结果，如果没有则使用空数组
  const formulaResults = state.formulaConfig.formulaResults || [];
  const [loading, setLoading] = useState(false);

  // 防止无限循环的标记
  const isInitializedRef = useRef(false);
  const lastDataHashRef = useRef<string>('');

  // 使用useCallback包装onRecalculate，避免依赖项变化
  const stableOnRecalculate = useCallback(() => {
    onRecalculate();
  }, [onRecalculate]);

  // 获取启用状态的自定义公式列表
  const fetchEnabledFormulas = async () => {
    try {
      setLoading(true);
      const result = await execute(
        () => customFormulaApi.getList({
          status: FormulaStatus.ENABLED,
          page: 1,
          pageSize: 100
        }),
        '获取自定义公式列表'
      );

      perfLog.debug('自定义公式API返回结果:', result);

      if (result && result.list && Array.isArray(result.list)) {
        setAvailableFormulas(result.list);
        perfLog.debug('获取到启用的自定义公式:', result.list.length);
        if (result.list.length === 0) {
          message.info('暂无启用的自定义公式，请先在公式管理页面创建并启用公式');
        }
      } else if (result === null) {
        // execute返回null表示API调用失败，错误已经被useAsyncError处理
        perfLog.warn('自定义公式API调用失败');
        setAvailableFormulas([]);
      } else {
        perfLog.warn('自定义公式数据格式异常:', result);
        setAvailableFormulas([]);
        message.warning('获取公式数据失败，数据格式异常');
      }
    } catch (error) {
      perfLog.error('获取自定义公式失败:', error);
      message.error('获取自定义公式失败，请稍后重试');
      setAvailableFormulas([]);
    } finally {
      setLoading(false);
    }
  };

  // 生成数据哈希值，用于检测数据变化
  const generateDataHash = useCallback(() => {
    const materialConfigCount = state.materialConfig.partMaterialConfigs ?
      Object.keys(state.materialConfig.partMaterialConfigs).length : 0;
    const processConfigCount = state.processConfig.partGroupProcessConfigs ?
      Object.keys(state.processConfig.partGroupProcessConfigs).length : 0;
    const accessoryCount = (state.accessoryConfig.accessories?.length || 0) +
      (state.accessoryConfig.giftBoxAccessories?.length || 0);
    const hasCalculatedImposition = state.packagingConfig.hasCalculatedImposition;

    return `${materialConfigCount}-${processConfigCount}-${accessoryCount}-${hasCalculatedImposition}`;
  }, [
    state.materialConfig.partMaterialConfigs,
    state.processConfig.partGroupProcessConfigs,
    state.accessoryConfig.accessories,
    state.accessoryConfig.giftBoxAccessories,
    state.packagingConfig.hasCalculatedImposition
  ]);

  // 组件初始化时获取公式列表和触发报价计算
  useEffect(() => {
    if (!isInitializedRef.current) {
      fetchEnabledFormulas();

      // 检查是否有已存在的公式配置，如果有则恢复formulaResults状态
      if (state.formulaConfig.formulas && state.formulaConfig.formulas.length > 0) {
        perfLog.debug('恢复已存在的公式配置:', state.formulaConfig.formulas.length);
        // 这里我们需要重新构建formulaResults，但由于缺少inputValues，暂时跳过
        // 实际应用中应该在FormulaConfig中保存完整的计算结果
      }

      // 检查是否有数据需要计算
      const currentHash = generateDataHash();
      if (currentHash !== '0-0-0-false') {
        perfLog.debug('组件初始化，触发报价计算');
        stableOnRecalculate();
      }

      isInitializedRef.current = true;
      lastDataHashRef.current = currentHash;
    }
  }, [generateDataHash, stableOnRecalculate, state.formulaConfig.formulas]);

  // 监听前面步骤的数据变化，自动重新计算报价
  useEffect(() => {
    if (!isInitializedRef.current) return;

    const currentHash = generateDataHash();

    // 只有当数据真正发生变化时才重新计算
    if (currentHash !== lastDataHashRef.current) {
      perfLog.debug('检测到配置数据变化，自动重新计算报价', {
        oldHash: lastDataHashRef.current,
        newHash: currentHash
      });

      // 使用防抖延迟执行，避免频繁计算
      const timeoutId = setTimeout(() => {
        stableOnRecalculate();
      }, 300);

      lastDataHashRef.current = currentHash;

      return () => clearTimeout(timeoutId);
    }
  }, [generateDataHash, stableOnRecalculate]);



  // 打开公式选择模态框
  const handleAddFormula = async () => {
    // 重新获取最新的公式列表
    await fetchEnabledFormulas();
    setFormulaModalVisible(true);
  };

  // 选择公式后打开参数输入模态框
  const handleFormulaSelect = async (formulaId: number) => {
    try {
      setLoading(true);
      const result = await execute(
        () => customFormulaApi.getDetail(formulaId),
        '获取公式详情'
      );

      if (result) {
        setSelectedFormula(result);
        setFormulaModalVisible(false);
        setFormulaInputModalVisible(true);

        // 初始化表单默认值
        const initialValues: Record<string, number> = {};
        result.attributes?.forEach((attr: any) => {
          initialValues[attr.name] = attr.value || 0;
        });
        formulaForm.setFieldsValue(initialValues);

        perfLog.debug('选择公式:', result.name, '属性数量:', result.attributes?.length);
      }
    } catch (error) {
      perfLog.error('获取公式详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算公式并确认添加
  const handleFormulaConfirm = async () => {
    if (!selectedFormula) return;

    try {
      const values = formulaForm.getFieldsValue();
      perfLog.debug('公式参数输入值:', values);

      // 计算公式结果
      const calculationResult = calculateFormulaResult(selectedFormula, values);

      if (calculationResult.error) {
        message.error(`公式计算失败: ${calculationResult.error}`);
        return;
      }

      // 添加到公式结果列表
      const newResults = [...formulaResults, calculationResult];

      perfLog.debug('更新公式结果列表:', {
        oldCount: formulaResults.length,
        newCount: newResults.length,
        newResult: calculationResult
      });

      // 更新公式配置，包含公式结果
      const updatedFormulas = [...(state.formulaConfig.formulas || []), selectedFormula];
      const totalFormulaCost = newResults.reduce((sum, result) => sum + result.finalCost, 0);

      onUpdate.formulaConfig({
        formulas: updatedFormulas,
        formulaResults: newResults,
        formulaCost: totalFormulaCost
      });

      perfLog.debug('更新公式配置:', {
        formulaCount: updatedFormulas.length,
        totalFormulaCost
      });

      // 关闭模态框并重置状态
      setFormulaInputModalVisible(false);
      setSelectedFormula(null);
      formulaForm.resetFields();

      message.success('自定义公式添加成功');
      perfLog.debug('公式计算结果:', calculationResult);

      // 重新计算总报价 - 使用稳定的回调
      stableOnRecalculate();

    } catch (error) {
      perfLog.error('公式确认失败:', error);
      message.error('公式确认失败，请检查输入参数');
    }
  };

  // 计算公式结果
  const calculateFormulaResult = (
    formula: CustomFormula,
    inputValues: Record<string, number>
  ): FormulaCalculationResult => {
    try {
      if (!formula.expression) {
        return {
          formula,
          inputValues,
          calculatedValue: 0,
          finalCost: formula.initialAmount || 0,
          error: '公式表达式为空'
        };
      }

      // 构建计算作用域
      const scope: Record<string, number> = {};

      // 添加基础信息（中文和拼音两种形式）
      const basicVars = {
        '数量': state.basicInfo.quantity || 0,
        '材料费': state.quotation.materialCost || 0,
        '工艺费': state.quotation.processCost || 0,
        '配件费': state.quotation.accessoryCost || 0,
        '加工费': state.quotation.processingFeeCost || 0,
      };

      // 添加中文变量名和对应的拼音变量名
      Object.entries(basicVars).forEach(([chineseName, value]) => {
        scope[chineseName] = value;
        scope[translateChineseToPinyin(chineseName)] = value;
      });

      // 添加用户输入的参数（中文和拼音两种形式）
      Object.entries(inputValues).forEach(([paramName, value]) => {
        scope[paramName] = value;
        scope[translateChineseToPinyin(paramName)] = value;
      });

      // 添加盒型属性到作用域
      state.basicInfo.attributes?.forEach((attr: any) => {
        const attrValue = parseFloat(attr.value) || 0;
        if (attr.code) {
          scope[attr.code] = attrValue;
          scope[translateChineseToPinyin(attr.code)] = attrValue;
        }
        if (attr.name) {
          scope[attr.name] = attrValue;
          scope[translateChineseToPinyin(attr.name)] = attrValue;
        }
      });

      perfLog.debug('公式计算作用域:', scope);
      perfLog.debug('原始表达式:', formula.expression);

      // 翻译中文变量名并计算
      const translatedExpression = translateChineseToPinyin(formula.expression);
      perfLog.debug('翻译后表达式:', translatedExpression);

      const calculatedValue = evaluate(translatedExpression, scope);

      if (typeof calculatedValue !== 'number' || isNaN(calculatedValue)) {
        return {
          formula,
          inputValues,
          calculatedValue: 0,
          finalCost: formula.initialAmount || 0,
          error: '计算结果不是有效数字'
        };
      }

      // 考虑起步金额限制
      const finalCost = Math.max(calculatedValue, formula.initialAmount || 0);

      return {
        formula,
        inputValues,
        calculatedValue,
        finalCost,
      };

    } catch (error: any) {
      perfLog.error('公式计算错误:', error);
      return {
        formula,
        inputValues,
        calculatedValue: 0,
        finalCost: formula.initialAmount || 0,
        error: `计算错误: ${error?.message || '未知错误'}`
      };
    }
  };

  // 删除公式结果
  const handleRemoveFormula = (index: number) => {
    const newResults = formulaResults.filter((_, i) => i !== index);

    // 更新公式配置
    const updatedFormulas = (state.formulaConfig.formulas || []).filter((_, i) => i !== index);
    const totalFormulaCost = newResults.reduce((sum, result) => sum + result.finalCost, 0);

    onUpdate.formulaConfig({
      formulas: updatedFormulas,
      formulaResults: newResults,
      formulaCost: totalFormulaCost
    });

    message.success('公式已删除');

    // 重新计算总报价 - 使用稳定的回调
    stableOnRecalculate();
  };

  // 一键复制报价功能
  const handleCopyQuotation = () => {
    const quotationText = generateQuotationText();
    navigator.clipboard.writeText(quotationText).then(() => {
      message.success('报价信息已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  // 打印报价单功能
  const handlePrintQuotation = async () => {
    try {
      const result = await executePrintQuotation(state);
      if (result.success) {
        message.success(result.message);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      message.error('打印失败，请稍后重试');
      perfLog.error('打印错误:', error);
    }
  };

  // 导出PDF功能
  const handleExportPDF = async () => {
    try {
      message.loading('正在生成PDF...', 0);

      // 使用专门的PDF导出工具
      const htmlContent = generateDetailedQuotationHTML(state);
      await exportQuotationToPDFFromHTML(htmlContent, {
        filename: `报价单_${state.basicInfo.name || '未命名项目'}_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.pdf`,
        format: 'a4',
        orientation: 'portrait',
        quality: 1.5
      });

      message.destroy();
      message.success('PDF导出成功');

    } catch (error) {
      message.destroy();
      message.error('PDF生成失败，请稍后重试');
      perfLog.error('PDF导出错误:', error);
    }
  };



  // 生成报价文本（结构化为简介和详情两部分）
  const generateQuotationText = (): string => {
    const lines: string[] = [];

    // 第一部分：简介
    lines.push('=== 盒型计算报价单（简介）===');
    lines.push('');
    lines.push('【项目概要】');
    lines.push(`项目名称: ${state.basicInfo.name || '未命名项目'}`);
    lines.push(`生产数量: ${state.basicInfo.quantity.toLocaleString()} 个`);
    lines.push(`总费用: ¥${state.quotation.totalCost.toFixed(2)}`);
    lines.push(`单价: ¥${(state.quotation.totalCost / state.basicInfo.quantity).toFixed(2)}/个`);

    // 费用构成概览
    lines.push('');
    lines.push('【费用构成】');
    if (state.quotation.materialCost > 0) {
      const percentage = ((state.quotation.materialCost / state.quotation.totalCost) * 100).toFixed(1);
      lines.push(`材料费用: ¥${state.quotation.materialCost.toFixed(2)} (${percentage}%)`);
    }
    if (state.quotation.processCost > 0) {
      const percentage = ((state.quotation.processCost / state.quotation.totalCost) * 100).toFixed(1);
      lines.push(`工艺费用: ¥${state.quotation.processCost.toFixed(2)} (${percentage}%)`);
    }
    if (state.quotation.accessoryCost > 0) {
      const percentage = ((state.quotation.accessoryCost / state.quotation.totalCost) * 100).toFixed(1);
      lines.push(`配件费用: ¥${state.quotation.accessoryCost.toFixed(2)} (${percentage}%)`);
    }
    if (state.quotation.processingFeeCost > 0) {
      const percentage = ((state.quotation.processingFeeCost / state.quotation.totalCost) * 100).toFixed(1);
      lines.push(`加工费费用: ¥${state.quotation.processingFeeCost.toFixed(2)} (${percentage}%)`);
    }
    if (state.quotation.formulaCost > 0) {
      const percentage = ((state.quotation.formulaCost / state.quotation.totalCost) * 100).toFixed(1);
      lines.push(`自定义费用: ¥${state.quotation.formulaCost.toFixed(2)} (${percentage}%)`);
    }

    // 盒型属性
    if (state.basicInfo.attributes && state.basicInfo.attributes.length > 0) {
      lines.push('');
      lines.push('【盒型规格】');
      state.basicInfo.attributes.forEach((attr: unknown) => {
        const attrData = attr as Record<string, unknown>;
        const name = attrData.name as string;
        const value = attrData.value as string;
        lines.push(`${name}: ${value}`);
      });
    }

    lines.push('');
    lines.push('='.repeat(50));
    lines.push('');

    // 第二部分：详情
    lines.push('=== 盒型计算报价单（详情）===');
    lines.push('');

    // 材料费用详情
    if (state.quotation.materialCost > 0) {
      lines.push('【材料费用明细】');

      // 尝试从packagingConfig中获取已计算的材料费用详情
      const existingMaterialCosts = (state.packagingConfig as any)?.materialCostDetails;
      if (existingMaterialCosts && Array.isArray(existingMaterialCosts) && existingMaterialCosts.length > 0) {
        existingMaterialCosts.forEach((detail: any, index: number) => {
          lines.push(`${index + 1}. ${formatMaterialType(detail.materialType || 'unknown')}: ${detail.materialName}`);
          lines.push(`   规格: ${formatMaterialSpec(detail.specification || 'unknown')}`);
          lines.push(`   用量: ${(detail.quantity || 0).toLocaleString()} ${getChineseUnit(detail.unit || '张')}`);
          lines.push(`   单价: ¥${(detail.unitPrice || 0).toFixed(2)}/${getChineseUnit(detail.unit || '张')}`);
          lines.push(`   小计: ¥${(detail.totalCost || 0).toFixed(2)}`);
          if (detail.partGroups && detail.partGroups.length > 0) {
            lines.push(`   适用部件: ${detail.partGroups.join('、')}`);
          }
          if (detail.impositionDetails && detail.impositionDetails.length > 0) {
            lines.push(`   拼版信息:`);
            detail.impositionDetails.forEach((imp: any) => {
              lines.push(`     ${imp.partGroupName}: ${imp.impositionX}×${imp.impositionY} = ${imp.totalImposition}个/张`);
              lines.push(`     材料尺寸: ${formatMaterialSize(imp.materialSize)}`);
              lines.push(`     利用率: ${(imp.efficiency || 0).toFixed(1)}%`);
            });
          }
          lines.push('');
        });
      } else if (state.materialConfig.partMaterialConfigs) {
        // 备用方案：如果没有计算好的详情，使用配置信息
        let materialIndex = 1;
        Object.entries(state.materialConfig.partMaterialConfigs).forEach(([partGroupKey, config]) => {
          if (config.materialCategory && config.materialName) {
            lines.push(`${materialIndex}. ${formatMaterialType(config.materialCategory)}: ${config.materialName}`);
            lines.push(`   规格: ${formatMaterialSpec(config.materialSpec || 'unknown')}`);
            lines.push(`   材料尺寸: ${formatMaterialSize(config.materialSize)}`);
            lines.push(`   适用部件: ${partGroupKey}`);
            if (config.structurePrice) {
              lines.push(`   结构价格: ¥${config.structurePrice.toFixed(2)}/平方米`);
            }
            lines.push('');
            materialIndex++;
          }
        });
      }
      lines.push(`材料费用合计: ¥${state.quotation.materialCost.toFixed(2)}`);
      lines.push('');
    }

    // 工艺费用详情
    if (state.quotation.processCost > 0) {
      lines.push('【工艺费用明细】');
      if (state.processConfig.partGroupProcessConfigs) {
        let processIndex = 1;
        Object.entries(state.processConfig.partGroupProcessConfigs).forEach(([partGroupKey, config]) => {
          const partGroupName = getPartGroupDisplayName(state, partGroupKey);
          let hasProcesses = false;

          // 各种工艺的详情
          const processTypes = ['printing', 'laminating', 'surfaceProcess', 'silkScreen', 'hotStamping', 'embossing', 'dieCutting', 'dieCuttingPlateFee'];
          const processNames: Record<string, string> = {
            printing: '印刷',
            laminating: '对裱',
            surfaceProcess: '覆膜',
            silkScreen: '丝印',
            hotStamping: '烫金',
            embossing: '凹凸',
            dieCutting: '模切',
            dieCuttingPlateFee: '刀版费'
          };

          processTypes.forEach(processType => {
            const configAsAny = config as any;
            const processes = configAsAny[processType];
            if (processes && Array.isArray(processes) && processes.length > 0) {
              if (!hasProcesses) {
                lines.push(`${processIndex}. 部件组: ${partGroupName}`);
                hasProcesses = true;
              }

              processes.forEach((process: any, idx: number) => {
                // 优化工艺名称获取逻辑
                let processName = '未知工艺';
                let processDetails: string[] = [];

                // 根据工艺类型获取具体名称和参数
                if (processType === 'printing') {
                  // 印刷工艺：显示机型名称和相关参数
                  processName = process.name || process.machineModel || '未知印刷机型';
                  if (process.parameters?.machineModel) {
                    processDetails.push(`机型: ${process.parameters.machineModel}`);
                  }
                  if (process.parameters?.ctpPlateFee && process.parameters.ctpPlateFee > 0) {
                    processDetails.push(`CTP板费: ¥${process.parameters.ctpPlateFee.toFixed(2)}`);
                  }
                  if (process.parameters?.spotColorFee && process.parameters.spotColorFee > 0) {
                    processDetails.push(`专色费: ¥${process.parameters.spotColorFee.toFixed(2)}`);
                  }
                  if (process.parameters?.colorCount && process.parameters.colorCount > 0) {
                    processDetails.push(`颜色数: ${process.parameters.colorCount}色`);
                  }
                } else if (processType === 'hotStamping') {
                  // 烫金工艺：显示具体烫金类型和详细参数
                  processName = process.name || '未知烫金类型';
                  if (process.hotStampingDimensions) {
                    const dims = process.hotStampingDimensions;
                    processDetails.push(`烫金尺寸: ${dims.length}×${dims.width}mm`);
                    processDetails.push(`烫金面积: ${((dims.length * dims.width) / 1000000).toFixed(4)}㎡`);
                  }
                  if (process.parameters?.materialPrice && process.parameters.materialPrice > 0) {
                    processDetails.push(`材料价格: ¥${process.parameters.materialPrice.toFixed(2)}/㎡`);
                  }
                  if (process.parameters?.salary && process.parameters.salary > 0) {
                    processDetails.push(`工资: ¥${process.parameters.salary.toFixed(2)}/张`);
                  }
                  if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                    processDetails.push(`开机费: ¥${process.parameters.setupFee.toFixed(2)}`);
                  }
                } else if (processType === 'embossing') {
                  // 凹凸工艺：显示具体类型和尺寸参数
                  processName = process.name || '未知凹凸工艺';
                  if (process.embossingDimensions) {
                    const dims = process.embossingDimensions;
                    processDetails.push(`凹凸尺寸: ${dims.length}×${dims.width}mm`);
                    processDetails.push(`凹凸面积: ${((dims.length * dims.width) / 1000000).toFixed(4)}㎡`);
                  }
                  if (process.hydraulicDimensions) {
                    const dims = process.hydraulicDimensions;
                    processDetails.push(`液压尺寸: ${dims.length}×${dims.width}mm`);
                    processDetails.push(`液压面积: ${((dims.length * dims.width) / 1000000).toFixed(4)}㎡`);
                  }
                  if (process.parameters?.textureVersion) {
                    processDetails.push(`压纹版: ${process.parameters.textureVersion}`);
                  }
                  if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                    processDetails.push(`开机费: ¥${process.parameters.setupFee.toFixed(2)}`);
                  }
                } else if (processType === 'laminating') {
                  // 对裱工艺：显示具体类型和参数
                  processName = process.name || '未知对裱工艺';
                  if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                    processDetails.push(`起步价: ¥${process.parameters.basePrice.toFixed(2)}`);
                  }
                  if (process.parameters?.materialType) {
                    processDetails.push(`材料类型: ${process.parameters.materialType}`);
                  }
                  if (process.parameters?.thickness) {
                    processDetails.push(`厚度: ${process.parameters.thickness}mm`);
                  }
                } else if (processType === 'surfaceProcess') {
                  // 覆膜工艺：显示具体类型和参数
                  processName = process.name || '未知覆膜工艺';
                  if (process.parameters?.filmType) {
                    processDetails.push(`膜类型: ${process.parameters.filmType}`);
                  }
                  if (process.parameters?.thickness) {
                    processDetails.push(`厚度: ${process.parameters.thickness}mm`);
                  }
                  if (process.parameters?.density) {
                    processDetails.push(`密度: ${process.parameters.density}`);
                  }
                  if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                    processDetails.push(`起步价: ¥${process.parameters.basePrice.toFixed(2)}`);
                  }
                } else if (processType === 'silkScreen') {
                  // 丝印工艺：显示具体类型和参数
                  processName = process.name || '未知丝印工艺';
                  if (process.parameters?.inkType) {
                    processDetails.push(`油墨类型: ${process.parameters.inkType}`);
                  }
                  if (process.parameters?.colorCount && process.parameters.colorCount > 0) {
                    processDetails.push(`颜色数: ${process.parameters.colorCount}色`);
                  }
                  if (process.parameters?.materialFee && process.parameters.materialFee > 0) {
                    processDetails.push(`材料费: ¥${process.parameters.materialFee.toFixed(2)}`);
                  }
                  if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                    processDetails.push(`开机费: ¥${process.parameters.setupFee.toFixed(2)}`);
                  }
                } else if (processType === 'dieCutting') {
                  // 模切工艺：显示具体类型和参数
                  processName = process.name || '未知模切工艺';
                  if (process.parameters?.machineType) {
                    processDetails.push(`机型: ${process.parameters.machineType}`);
                  }
                  if (process.parameters?.difficulty) {
                    processDetails.push(`难度: ${process.parameters.difficulty}`);
                  }
                  if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                    processDetails.push(`开机费: ¥${process.parameters.setupFee.toFixed(2)}`);
                  }
                } else if (processType === 'dieCuttingPlateFee') {
                  // 刀版费：显示具体类型和参数
                  processName = process.name || '未知刀版费';
                  if (process.parameters?.plateType) {
                    processDetails.push(`版型: ${process.parameters.plateType}`);
                  }
                  if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                    processDetails.push(`起步金额: ¥${process.parameters.basePrice.toFixed(2)}`);
                  }
                  if (process.parameters?.impositionQuantity && process.parameters.impositionQuantity > 0) {
                    processDetails.push(`按拼版数量: ${process.parameters.impositionQuantity}`);
                  }
                  if (process.parameters?.area && process.parameters.area > 0) {
                    processDetails.push(`版面积: ${process.parameters.area.toFixed(2)}㎡`);
                  }
                } else {
                  // 其他工艺：使用通用逻辑并尝试提取通用参数
                  processName = process.name || process.processName || process.machineModel || processNames[processType];

                  // 通用参数提取
                  if (process.parameters) {
                    if (process.parameters.setupFee && process.parameters.setupFee > 0) {
                      processDetails.push(`开机费: ¥${process.parameters.setupFee.toFixed(2)}`);
                    }
                    if (process.parameters.basePrice && process.parameters.basePrice > 0) {
                      processDetails.push(`起步价: ¥${process.parameters.basePrice.toFixed(2)}`);
                    }
                    if (process.parameters.materialFee && process.parameters.materialFee > 0) {
                      processDetails.push(`材料费: ¥${process.parameters.materialFee.toFixed(2)}`);
                    }
                  }
                }

                const quantity = process.quantity || 0;
                const unitPrice = process.unitPrice || 0;
                const totalPrice = process.totalPrice || 0;
                const knifeMoldFee = process.knifeMoldFee;
                const originalUnit = process.unit || '个';

                // 从计价单位中提取数量单位
                const getQuantityUnit = (unit: string): string => {
                  if (unit.includes('元/')) {
                    const quantityUnit = unit.replace('元/', '');
                    return getChineseUnit(quantityUnit);
                  }
                  return getChineseUnit(unit);
                };

                const quantityUnit = getQuantityUnit(originalUnit);

                lines.push(`   ${processNames[processType]}${processes.length > 1 ? `(${idx + 1})` : ''}: ${processName}`);

                // 显示工艺详细参数
                if (processDetails.length > 0) {
                  processDetails.forEach(detail => {
                    lines.push(`     ${detail}`);
                  });
                }

                lines.push(`     数量: ${quantity.toLocaleString()} ${quantityUnit}`);
                lines.push(`     单价: ¥${unitPrice.toFixed(2)}/${quantityUnit}`);
                lines.push(`     小计: ¥${totalPrice.toFixed(2)}`);

                // 刀版费（仅模切工艺）
                if (processType === 'dieCutting' && knifeMoldFee && knifeMoldFee > 0) {
                  lines.push(`     刀版费: ¥${knifeMoldFee.toFixed(2)}`);
                }
              });
            }
          });

          if (hasProcesses) {
            lines.push('');
            processIndex++;
          }
        });
      }
      lines.push(`工艺费用合计: ¥${state.quotation.processCost.toFixed(2)}`);
      lines.push('');
    }

    // 配件费用详情
    if (state.quotation.accessoryCost > 0) {
      lines.push('【配件费用明细】');
      let accessoryIndex = 1;

      if (state.accessoryConfig.accessories && state.accessoryConfig.accessories.length > 0) {
        lines.push('普通配件:');
        state.accessoryConfig.accessories.forEach((accessory: unknown) => {
          const accessoryData = accessory as Record<string, unknown>;

          // 优先使用materialName，然后是name，最后是fallback
          let materialName = '未知配件';
          if (accessoryData.materialName) {
            materialName = accessoryData.materialName as string;
          } else if (accessoryData.name) {
            materialName = accessoryData.name as string;
          } else if ((accessoryData as any).material?.name) {
            materialName = (accessoryData as any).material.name;
          } else if ((accessoryData as any).material?.materialName) {
            materialName = (accessoryData as any).material.materialName;
          } else if (accessoryData.processName) {
            materialName = accessoryData.processName as string;
          }

          const quantity = (accessoryData.quantity as number) || 0;
          const originalUnit = (accessoryData.unit as string) || '个';
          const unitPrice = (accessoryData.unitPrice as number) || 0;
          const totalPrice = (accessoryData.totalPrice as number) || 0;

          // 从计价单位中提取数量单位
          const getAccessoryQuantityUnit = (unit: string): string => {
            if (unit.includes('元/')) {
              const quantityUnit = unit.replace('元/', '');
              return getChineseUnit(quantityUnit);
            }
            return getChineseUnit(unit);
          };

          const quantityUnit = getAccessoryQuantityUnit(originalUnit);

          lines.push(`${accessoryIndex}. ${materialName}`);
          lines.push(`   数量: ${quantity.toLocaleString()} ${quantityUnit}`);
          lines.push(`   单价: ¥${unitPrice.toFixed(2)}/${quantityUnit}`);
          lines.push(`   小计: ¥${totalPrice.toFixed(2)}`);

          // 如果有额外参数信息，显示出来
          if (accessoryData.parameters) {
            const params = accessoryData.parameters as Record<string, any>;
            if (params.length && params.width) {
              lines.push(`   规格: ${params.length}×${params.width}mm`);
            } else if (params.metersPerBox) {
              lines.push(`   每盒米数: ${params.metersPerBox}米`);
            }
          }
          lines.push('');
          accessoryIndex++;
        });
      }

      if (state.accessoryConfig.giftBoxAccessories && state.accessoryConfig.giftBoxAccessories.length > 0) {
        lines.push('礼盒配件:');
        state.accessoryConfig.giftBoxAccessories.forEach((giftBoxAccessory: unknown) => {
          const giftBoxAccessoryData = giftBoxAccessory as Record<string, unknown>;

          // 优先使用materialName，然后是name，最后是fallback
          let materialName = '未知配件';
          if (giftBoxAccessoryData.materialName) {
            materialName = giftBoxAccessoryData.materialName as string;
          } else if (giftBoxAccessoryData.name) {
            materialName = giftBoxAccessoryData.name as string;
          } else if ((giftBoxAccessoryData as any).material?.name) {
            materialName = (giftBoxAccessoryData as any).material.name;
          } else if ((giftBoxAccessoryData as any).material?.materialName) {
            materialName = (giftBoxAccessoryData as any).material.materialName;
          } else if (giftBoxAccessoryData.processName) {
            materialName = giftBoxAccessoryData.processName as string;
          }

          const quantity = (giftBoxAccessoryData.quantity as number) || 0;
          const originalUnit = (giftBoxAccessoryData.unit as string) || '个';
          const unitPrice = (giftBoxAccessoryData.unitPrice as number) || 0;
          const totalPrice = (giftBoxAccessoryData.totalPrice as number) || 0;

          // 从计价单位中提取数量单位
          const getGiftBoxAccessoryQuantityUnit = (unit: string): string => {
            if (unit.includes('元/')) {
              const quantityUnit = unit.replace('元/', '');
              return getChineseUnit(quantityUnit);
            }
            return getChineseUnit(unit);
          };

          const quantityUnit = getGiftBoxAccessoryQuantityUnit(originalUnit);

          lines.push(`${accessoryIndex}. ${materialName}`);
          lines.push(`   数量: ${quantity.toLocaleString()} ${quantityUnit}`);
          lines.push(`   单价: ¥${unitPrice.toFixed(2)}/${quantityUnit}`);
          lines.push(`   小计: ¥${totalPrice.toFixed(2)}`);

          // 显示详细的礼盒配件参数
          // 根据单位推断计算方式
          const unit = giftBoxAccessoryData.unit || '个';
          if (unit === '元/立方') {
            lines.push(`   计算方式: 按体积计算`);

            // 显示计算数量（体积）
            if (giftBoxAccessoryData.quantity) {
              lines.push(`   材料体积: ${(giftBoxAccessoryData.quantity as number).toFixed(4)}立方米`);
            }

            // 显示库存尺寸（如果使用现货尺寸）
            const material = giftBoxAccessoryData.material as any;
            if (material?.isStockSize && material?.stockLength && material?.stockWidth) {
              lines.push(`   库存尺寸: ${material.stockLength}×${material.stockWidth}mm`);
            }
          } else if (unit === '元/平方') {
            lines.push(`   计算方式: 按表面积计算`);

            // 显示计算数量（表面积）
            if (giftBoxAccessoryData.quantity) {
              lines.push(`   表面积: ${(giftBoxAccessoryData.quantity as number).toFixed(4)}平方米`);
            }
          }

          // 显示用户输入的盒子尺寸
          if (giftBoxAccessoryData.parameters) {
            const params = giftBoxAccessoryData.parameters as Record<string, any>;
            if (params.length && params.width && params.height) {
              lines.push(`   盒子尺寸: ${params.length}×${params.width}×${params.height}mm`);
            }
            if (params.spacing) {
              lines.push(`   材料间距: ${params.spacing}mm`);
            }
          }

          // 显示基础配件信息
          const material = giftBoxAccessoryData.material as any;
          if (material?.isStockSize) {
            lines.push(`   使用现货尺寸: 是`);
          }
          lines.push('');
          accessoryIndex++;
        });
      }

      lines.push(`配件费用合计: ¥${state.quotation.accessoryCost.toFixed(2)}`);
      lines.push('');
    }

    // 加工费费用详情
    if (state.quotation.processingFeeCost > 0) {
      lines.push('【加工费费用明细】');
      let processingFeeIndex = 1;

      // 可选加工费
      if (state.processingFeeConfig?.customFees && state.processingFeeConfig.customFees.length > 0) {
        lines.push('可选加工费:');
        state.processingFeeConfig.customFees.forEach((fee) => {
          lines.push(`${processingFeeIndex}. ${fee.name}`);
          lines.push(`   数量: ${fee.quantity.toLocaleString()} ${fee.unit.replace('元/', '')}`);
          lines.push(`   单价: ¥${fee.unitPrice.toFixed(2)}/${fee.unit.replace('元/', '')}`);
          lines.push(`   起步价: ¥${fee.basePrice.toFixed(2)}`);
          lines.push(`   小计: ¥${fee.totalPrice.toFixed(2)}`);
          if (fee.remark) {
            lines.push(`   备注: ${fee.remark}`);
          }
          lines.push('');
          processingFeeIndex++;
        });
      }

      // 固定参数选项加工费
      if (state.processingFeeConfig?.fixedFees && state.processingFeeConfig.fixedFees.length > 0) {
        lines.push('固定参数选项加工费:');
        state.processingFeeConfig.fixedFees.forEach((fee) => {
          lines.push(`${processingFeeIndex}. ${fee.name}`);
          lines.push(`   数量: ${fee.quantity.toLocaleString()} ${fee.unit.replace('元/', '')}`);
          lines.push(`   单价: ¥${fee.unitPrice.toFixed(2)}/${fee.unit.replace('元/', '')}`);
          lines.push(`   起步价: ¥${fee.basePrice.toFixed(2)}`);
          lines.push(`   小计: ¥${fee.totalPrice.toFixed(2)}`);
          lines.push('');
          processingFeeIndex++;
        });
      }

      lines.push(`加工费费用合计: ¥${state.quotation.processingFeeCost.toFixed(2)}`);
      lines.push('');
    }

    // 自定义费用详情
    if (state.quotation.formulaCost > 0) {
      lines.push('【自定义费用明细】');
      formulaResults.forEach((result, index) => {
        lines.push(`${index + 1}. ${result.formula.name}`);
        if (result.formula.expression) {
          lines.push(`   公式表达式: ${result.formula.expression}`);
        }
        if (result.formula.initialAmount && result.formula.initialAmount > 0) {
          lines.push(`   起步金额: ¥${result.formula.initialAmount.toFixed(2)}`);
        }

        // 显示输入参数
        if (result.inputValues && Object.keys(result.inputValues).length > 0) {
          lines.push(`   输入参数:`);
          Object.entries(result.inputValues).forEach(([key, value]) => {
            lines.push(`     ${key}: ${value}`);
          });
        }

        lines.push(`   计算结果: ¥${result.calculatedValue.toFixed(2)}`);
        lines.push(`   最终费用: ¥${result.finalCost.toFixed(2)}`);

        if (result.error) {
          lines.push(`   ⚠️ 错误: ${result.error}`);
        }
        lines.push('');
      });
      lines.push(`自定义费用合计: ¥${state.quotation.formulaCost.toFixed(2)}`);
      lines.push('');
    }

    // 最终费用汇总
    lines.push('【最终费用汇总】');
    lines.push(`材料费用: ¥${state.quotation.materialCost.toFixed(2)}`);
    lines.push(`工艺费用: ¥${state.quotation.processCost.toFixed(2)}`);
    lines.push(`配件费用: ¥${state.quotation.accessoryCost.toFixed(2)}`);
    lines.push(`加工费费用: ¥${state.quotation.processingFeeCost.toFixed(2)}`);
    lines.push(`自定义费用: ¥${state.quotation.formulaCost.toFixed(2)}`);
    lines.push('-'.repeat(30));
    lines.push(`总计: ¥${state.quotation.totalCost.toFixed(2)}`);
    lines.push(`单价: ¥${(state.quotation.totalCost / state.basicInfo.quantity).toFixed(2)}/个`);
    lines.push('');

    // 交期和备注信息
    lines.push('【其他信息】');
    lines.push(`预计交期: 根据实际订单量确定`);
    lines.push(`备注: 此报价仅供参考，最终价格以实际订单为准`);
    lines.push(`生成时间: ${new Date().toLocaleString('zh-CN')}`);

    return lines.join('\n');
  };

  // 费用明细表格数据
  const quotationTableData = [
    {
      key: '1',
      name: '材料费用',
      amount: state.quotation.materialCost,
      description: '包含面纸、灰板、瓦楞等材料成本'
    },
    {
      key: '2',
      name: '工艺费用',
      amount: state.quotation.processCost,
      description: '包含印刷、覆膜、模切等工艺成本'
    },
    {
      key: '3',
      name: '配件费用',
      amount: state.quotation.accessoryCost,
      description: '包含普通配件和礼盒配件成本'
    },
    {
      key: '4',
      name: '加工费费用',
      amount: state.quotation.processingFeeCost,
      description: '包含可选加工费和固定参数选项加工费'
    },
    {
      key: '5',
      name: '公式费用',
      amount: state.quotation.formulaCost,
      description: '自定义公式计算的额外费用'
    }
  ];

  const quotationTableColumns = [
    {
      title: '费用项目',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: { description: string }) => (
        <Tooltip title={record.description}>
          <Space>
            {name}
            <InfoCircleOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
          </Space>
        </Tooltip>
      )
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (value: number) => (
        <Text strong style={{ color: value > 0 ? '#52c41a' : '#8c8c8c' }}>
          {formatCurrency(value)}
        </Text>
      ),
      align: 'right' as const
    }
  ];

  return (
    <div className="quotation-step">
      <Alert
        message="确认报价"
        description="查看各步骤费用明细，添加自定义公式，确认最终报价。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Row gutter={[16, 16]}>
        {/* 左侧：详细费用展示 */}
        <Col span={16}>
          {/* 基础信息 */}
          <Card title="项目信息" style={{ marginBottom: 16 }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="项目名称">
                {state.basicInfo.name || '未命名项目'}
              </Descriptions.Item>
              <Descriptions.Item label="数量">
                {state.basicInfo.quantity.toLocaleString()} 个
              </Descriptions.Item>
              {state.basicInfo.attributes && state.basicInfo.attributes.length > 0 && (
                <Descriptions.Item label="属性" span={2}>
                  {state.basicInfo.attributes.map((attr: unknown, index: number) => {
                    const attrData = attr as Record<string, unknown>;
                    const name = attrData.name as string;
                    const value = attrData.value as string;
                    return (
                      <Tag key={index} style={{ marginRight: 8 }}>
                        {name}: {value}
                      </Tag>
                    );
                  })}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          {/* 费用明细折叠面板 */}
          <Collapse
            defaultActiveKey={['summary']}
            style={{ marginBottom: 16 }}
            items={[
              {
                key: 'summary',
                label: (
                  <Space>
                    <Title level={5} style={{ margin: 0 }}>费用汇总</Title>
                    <Tag color="blue">{formatCurrency(state.quotation.totalCost)}</Tag>
                  </Space>
                ),
                children: (
                  <>
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Table
                          columns={quotationTableColumns}
                          dataSource={quotationTableData}
                          pagination={false}
                          size="small"
                          summary={() => (
                            <Table.Summary.Row>
                              <Table.Summary.Cell index={0}>
                                <Text strong>总计</Text>
                              </Table.Summary.Cell>
                              <Table.Summary.Cell index={1} align="right">
                                <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
                                  {formatCurrency(state.quotation.totalCost)}
                                </Text>
                              </Table.Summary.Cell>
                            </Table.Summary.Row>
                          )}
                        />
                      </Col>
                      {/* 报价统计信息 */}
                      <Col span={12} >
                        <Card title="报价统计" size="small" style={{ height: '100%' }}>
                          <Descriptions column={1} size="small">
                            <Descriptions.Item label="项目数量">
                              <Text strong>{state.basicInfo.quantity.toLocaleString()} 个</Text>
                            </Descriptions.Item>
                            <Descriptions.Item label="总费用">
                              <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
                                {formatCurrency(state.quotation.totalCost)}
                              </Text>
                            </Descriptions.Item>
                            <Descriptions.Item label="单价">
                              <Text strong style={{ color: '#52c41a' }}>
                                {formatCurrency(state.quotation.totalCost / state.basicInfo.quantity)}/个
                              </Text>
                            </Descriptions.Item>
                            <Descriptions.Item label="费用构成">
                              <div>
                                {state.quotation.materialCost > 0 && (
                                  <div style={{ fontSize: '12px' }}>
                                    材料: {((state.quotation.materialCost / state.quotation.totalCost) * 100).toFixed(1)}%
                                  </div>
                                )}
                                {state.quotation.processCost > 0 && (
                                  <div style={{ fontSize: '12px' }}>
                                    工艺: {((state.quotation.processCost / state.quotation.totalCost) * 100).toFixed(1)}%
                                  </div>
                                )}
                                {state.quotation.accessoryCost > 0 && (
                                  <div style={{ fontSize: '12px' }}>
                                    配件: {((state.quotation.accessoryCost / state.quotation.totalCost) * 100).toFixed(1)}%
                                  </div>
                                )}
                                {state.quotation.processingFeeCost > 0 && (
                                  <div style={{ fontSize: '12px' }}>
                                    加工费: {((state.quotation.processingFeeCost / state.quotation.totalCost) * 100).toFixed(1)}%
                                  </div>
                                )}
                                {state.quotation.formulaCost > 0 && (
                                  <div style={{ fontSize: '12px' }}>
                                    自定义: {((state.quotation.formulaCost / state.quotation.totalCost) * 100).toFixed(1)}%
                                  </div>
                                )}
                              </div>
                            </Descriptions.Item>
                          </Descriptions>
                        </Card>
                      </Col>
                    </Row>
                  </>
                )
              },
              ...(state.quotation.materialCost > 0 ? [{
                key: 'material',
                label: (
                  <Space>
                    <span>材料费用详情</span>
                    <Tag color="green">{formatCurrency(state.quotation.materialCost)}</Tag>
                  </Space>
                ),
                children: <MaterialCostDetail state={state} />
              }] : []),
              ...(state.quotation.processCost > 0 ? [{
                key: 'process',
                label: (
                  <Space>
                    <span>工艺费用详情</span>
                    <Tag color="orange">{formatCurrency(state.quotation.processCost)}</Tag>
                  </Space>
                ),
                children: <ProcessCostDetail state={state} />
              }] : []),
              ...(state.quotation.accessoryCost > 0 ? [{
                key: 'accessory',
                label: (
                  <Space>
                    <span>配件费用详情</span>
                    <Tag color="purple">{formatCurrency(state.quotation.accessoryCost)}</Tag>
                  </Space>
                ),
                children: <AccessoryCostDetail state={state} />
              }] : []),
              ...(state.quotation.processingFeeCost > 0 ? [{
                key: 'processingFee',
                label: (
                  <Space>
                    <span>加工费费用详情</span>
                    <Tag color="cyan">{formatCurrency(state.quotation.processingFeeCost)}</Tag>
                  </Space>
                ),
                children: <ProcessingFeeCostDetail state={state} />
              }] : [])
            ]}
          />
        </Col>

        {/* 右侧：自定义公式和操作 */}
        <Col span={8}>
          {/* 自定义公式 */}
          <Card
            title={
              <Space>
                <FormOutlined />
                <span>自定义费用 ({formulaResults.length})</span>
              </Space>
            }
            extra={
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleAddFormula}
                loading={loading}
              >
                新增
              </Button>
            }
            style={{ marginBottom: 16 }}
          >
            <FormulaCostDetail
              formulaResults={formulaResults}
              onRemoveFormula={handleRemoveFormula}
            />
          </Card>

          {/* 操作按钮 */}
          <Card title="操作" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Tooltip title="重新计算所有费用项目的总价">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={stableOnRecalculate}
                  loading={state.isCalculating}
                  block
                  size="large"
                >
                  重新计算报价
                </Button>
              </Tooltip>

              <Tooltip title="复制详细报价信息到剪贴板">
                <Button
                  type="primary"
                  icon={<CopyOutlined />}
                  onClick={handleCopyQuotation}
                  block
                  size="large"
                  disabled={state.quotation.totalCost <= 0}
                >
                  一键复制报价
                </Button>
              </Tooltip>

              <Divider style={{ margin: '8px 0' }} />

              <Tooltip title="打印当前报价单">
                <Button
                  icon={<PrinterOutlined />}
                  onClick={handlePrintQuotation}
                  block
                  disabled={state.quotation.totalCost <= 0}
                >
                  打印报价单
                </Button>
              </Tooltip>

              <Tooltip title="导出报价单为PDF文件">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExportPDF}
                  block
                  disabled={state.quotation.totalCost <= 0}
                >
                  导出PDF
                </Button>
              </Tooltip>
            </Space>
          </Card>


        </Col>
      </Row>

      {/* 公式选择模态框 */}
      <Modal
        title="选择自定义公式"
        open={formulaModalVisible}
        onCancel={() => setFormulaModalVisible(false)}
        footer={null}
        width={800}
      >
        {availableFormulas.length === 0 && !loading ? (
          <Alert
            message="暂无可用的自定义公式"
            description="请先在自定义公式管理页面创建并启用公式，然后重新打开此对话框。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
            action={
              <Button size="small" onClick={fetchEnabledFormulas}>
                刷新列表
              </Button>
            }
          />
        ) : (
          <Table
            dataSource={availableFormulas.map(formula => ({ ...formula, key: formula.id }))}
            columns={[
              {
                title: '公式名称',
                dataIndex: 'name',
                key: 'name',
                align: 'center' as const,
                render: (name: string, record: CustomFormula) => (
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{name}</div>
                    {record.expression && (
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        表达式: {record.expression}
                      </div>
                    )}
                  </div>
                )
              },
              {
                title: '起步金额',
                dataIndex: 'initialAmount',
                key: 'initialAmount',
                align: 'center' as const,
                render: (value: number) => (
                  <Tag color={value > 0 ? 'orange' : 'default'}>
                    {formatCurrency(value)}
                  </Tag>
                )
              },
              {
                title: '参数数量',
                key: 'attributeCount',
                align: 'center' as const,
                render: (_, record: CustomFormula) => (
                  <Tag color="blue">
                    {record._count?.attributes || 0} 个参数
                  </Tag>
                )
              },
              {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
                align: 'center' as const,
                render: (status: number) => (
                  <Tag color={status === FormulaStatus.ENABLED ? 'green' : 'default'}>
                    {status === FormulaStatus.ENABLED ? '已启用' : '已禁用'}
                  </Tag>
                )
              },
              {
                title: '操作',
                key: 'action',
                align: 'center' as const,
                render: (_, record: CustomFormula) => (
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => handleFormulaSelect(record.id)}
                    disabled={record.status !== FormulaStatus.ENABLED}
                  >
                    选择
                  </Button>
                )
              }
            ]}
            pagination={false}
            loading={loading}
            size="small"
            locale={{
              emptyText: loading ? '加载中...' : '暂无数据'
            }}
          />
        )}
      </Modal>

      {/* 公式参数输入模态框 */}
      <Modal
        title={`设置公式参数 - ${selectedFormula?.name}`}
        open={formulaInputModalVisible}
        onOk={handleFormulaConfirm}
        onCancel={() => {
          setFormulaInputModalVisible(false);
          setSelectedFormula(null);
          formulaForm.resetFields();
        }}
        width={600}
      >
        {selectedFormula && (
          <div>
            <Alert
              message="公式信息"
              description={
                <div>
                  <div>公式名称: {selectedFormula.name}</div>
                  <div>起步金额: {formatCurrency(selectedFormula.initialAmount)}</div>
                  {selectedFormula.expression && (
                    <div>计算表达式: {selectedFormula.expression}</div>
                  )}
                </div>
              }
              type="info"
              style={{ marginBottom: 16 }}
            />

            <Form form={formulaForm} layout="vertical">
              <Row gutter={[16, 16]}>
                {selectedFormula.attributes?.map((attr: any) => (
                  <Col span={8} key={attr.name}>
                    <Form.Item
                      name={attr.name}
                      label={attr.name}
                      rules={[{ required: true, message: `请输入${attr.name}` }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder={`请输入${attr.name}`}
                        min={0}
                        precision={2}
                      />
                    </Form.Item>
                  </Col>
                ))}
              </Row>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};



// 材料费用详情组件
const MaterialCostDetail: React.FC<{ state: CalculationState }> = ({ state }) => {
  // 使用工具函数进行格式化和转换

  // 获取拼版结果数据
  const impositionResults = useMemo(() => {
    const results = [
      ...(state.packagingConfig.faceImpositionResults || []),
      ...(state.packagingConfig.greyImpositionResults || [])
    ];
    return results;
  }, [state.packagingConfig.faceImpositionResults, state.packagingConfig.greyImpositionResults]);

  // 计算材料费用详情（集成ImpositionLogicDisplay的实际价格计算逻辑）
  const materialCostDetails = useMemo(() => {
    if (!state.materialConfig.partMaterialConfigs || impositionResults.length === 0) {
      return [];
    }

    const details: MaterialCostDetail[] = [];

    // 尝试从packagingConfig中获取已计算的材料费用详情
    const packagingConfigAsAny = state.packagingConfig as any;
    const existingMaterialCosts = packagingConfigAsAny?.materialCostDetails;

    if (existingMaterialCosts && Array.isArray(existingMaterialCosts) && existingMaterialCosts.length > 0) {
      // 如果已有计算结果，直接使用并添加拼版详情
      existingMaterialCosts.forEach((costDetail: any) => {
        // 查找对应的拼版结果

        const relatedImpositionResults = impositionResults.filter(result => {
          const config = state.materialConfig.partMaterialConfigs![result.partGroup.id];

          // 由于costDetail中没有materialCategory字段，只根据materialName进行匹配
          return config && config.materialName === costDetail.materialName;
        });

        const impositionDetails = relatedImpositionResults.map(result => ({
          partGroupName: result.partGroup.name,
          impositionX: result.impositionX || 0,
          impositionY: result.impositionY || 0,
          totalImposition: result.totalImposition || 0,
          efficiency: result.efficiency || 0,
          sheetsNeeded: result.sheetsNeeded || 0,
          materialSize: {
            width: result.materialWidth || 0,
            height: result.materialLength || 0
          }
        }));

        details.push({
          materialType: costDetail.materialType,
          materialName: costDetail.materialName,
          specification: getSpecDisplayName(costDetail.specification || ''),
          quantity: costDetail.quantity || 0,
          unit: costDetail.unit || '张',
          unitPrice: costDetail.unitPrice || 0,
          totalCost: costDetail.totalCost || 0,
          sheetsNeeded: costDetail.sheetsNeeded || 0,
          partGroups: costDetail.partGroups || [],
          impositionDetails
        });
      });

      return details;
    }

    // 如果没有已计算的结果，则进行计算
    // 按材料分组统计
    const materialGroups: Record<string, {
      materialType: string;
      materialName: string;
      specification: string;
      materialId: number;
      materialCategory: string;
      totalSheets: number;
      totalMaterialArea: number;
      partGroups: string[];
      impositionResults: any[];
      materialSize?: { width: number; height: number };
      structurePrice?: number; // 瓦楞材料的结构价格
      materialWeight?: number; // 材料克重
      materialPrice?: number; // 材料价格
      materialUnit?: string; // 材料单位
    }> = {};

    // 遍历拼版结果，按材料分组
    impositionResults.forEach(result => {
      const partGroupId = result.partGroup.id;
      const config = state.materialConfig.partMaterialConfigs![partGroupId];

      if (!config || !config.materialName) return;

      const groupKey = `${config.materialCategory}_${config.materialId}_${config.materialSpec}`;

      if (!materialGroups[groupKey]) {
        const materialTypeMap: Record<string, string> = {
          'paper': '面纸',
          'specialPaper': '特种纸',
          'greyBoard': '灰板',
          'corrugated': '瓦楞'
        };

        materialGroups[groupKey] = {
          materialType: materialTypeMap[config.materialCategory || ''] || '未知类型',
          materialName: config.materialName,
          specification: getSpecDisplayName(config.materialSpec || ''),
          materialId: config.materialId || 0,
          materialCategory: config.materialCategory || '',
          totalSheets: 0,
          totalMaterialArea: 0,
          partGroups: [],
          impositionResults: [],
          materialSize: config.materialSize,
          structurePrice: config.structurePrice,
          materialWeight: (config as Record<string, any>).materialWeight,
          materialPrice: (config as Record<string, any>).materialPrice,
          materialUnit: (config as Record<string, any>).materialUnit
        };
      }

      materialGroups[groupKey].totalSheets += result.sheetsNeeded || 0;
      materialGroups[groupKey].totalMaterialArea += (result.materialArea || 0) * (result.sheetsNeeded || 0);
      if (!materialGroups[groupKey].partGroups.includes(result.partGroup.name)) {
        materialGroups[groupKey].partGroups.push(result.partGroup.name);
      }
      materialGroups[groupKey].impositionResults.push(result);
    });

    // 为每个材料组计算费用（参考ImpositionLogicDisplay的实际计算逻辑）
    Object.values(materialGroups).forEach(group => {
      let quantity = 0;
      let unit = '张';
      let unitPrice = 0;
      let totalCost = 0;

      // 瓦楞材料使用配置中的结构价格
      if (group.materialCategory === 'corrugated' && group.structurePrice) {
        const totalAreaM2 = group.totalMaterialArea / 1000000; // 转换mm²到m²
        quantity = totalAreaM2;
        unit = 'm²';
        unitPrice = group.structurePrice;
        totalCost = quantity * unitPrice;
      } else if (group.materialPrice && group.materialUnit) {
        // 其他材料使用实际价格数据
        switch (group.materialUnit) {
          case '元/吨':
            if (group.materialWeight && group.materialSize) {
              // 按吨计价：面积(m²) × 克重(g/m²) × 张数 × 价格(元/吨) ÷ 1,000,000
              const areaM2 = (group.materialSize.width * group.materialSize.height) / 1000000;
              const weightTons = (areaM2 * group.materialWeight * group.totalSheets) / 1000000;
              quantity = weightTons;
              unit = '吨';
              unitPrice = group.materialPrice;
              totalCost = quantity * unitPrice;
            } else {
              // 缺少克重信息，按张计算
              quantity = group.totalSheets;
              unit = '张(缺少克重)';
              unitPrice = 0;
              totalCost = 0;
            }
            break;

          case '元/平方':
          case '元/平方米':
            // 按平方米计价：面积(m²) × 张数 × 价格(元/m²)
            if (group.materialSize) {
              const areaM2 = (group.materialSize.width * group.materialSize.height) / 1000000;
              quantity = areaM2 * group.totalSheets;
              unit = 'm²';
              unitPrice = group.materialPrice;
              totalCost = quantity * unitPrice;
            } else {
              quantity = group.totalSheets;
              unit = '张';
              unitPrice = 0;
              totalCost = 0;
            }
            break;

          case '元/张':
          default:
            // 按张计价：张数 × 价格(元/张)
            quantity = group.totalSheets;
            unit = '张';
            unitPrice = group.materialPrice;
            totalCost = quantity * unitPrice;
            break;
        }
      } else {
        // 没有价格信息，显示数量但价格为0
        quantity = group.totalSheets;
        unit = '张';
        unitPrice = 0;
        totalCost = 0;
      }

      // 构建拼版详情
      const impositionDetails = group.impositionResults.map(result => ({
        partGroupName: result.partGroup.name,
        impositionX: result.impositionX || 0,
        impositionY: result.impositionY || 0,
        totalImposition: result.totalImposition || 0,
        efficiency: result.efficiency || 0,
        sheetsNeeded: result.sheetsNeeded || 0,
        materialSize: {
          width: result.materialWidth || 0,
          height: result.materialLength || 0
        }
      }));

      details.push({
        materialType: group.materialType,
        materialName: group.materialName,
        specification: group.specification,
        quantity,
        unit,
        unitPrice,
        totalCost,
        sheetsNeeded: group.totalSheets,
        partGroups: group.partGroups,
        impositionDetails
      });
    });

    return details;
  }, [state.materialConfig.partMaterialConfigs, impositionResults]);

  // 渲染材料详情展开行
  const renderMaterialDetails = (record: MaterialCostDetail) => {
    if (!record.impositionDetails || record.impositionDetails.length === 0) {
      return (
        <div style={{ padding: '8px 0' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            暂无拼版详情信息
          </Text>
        </div>
      );
    }

    // 计算开数信息
    const getKaiShuInfo = (impositionX: number, impositionY: number) => {
      const totalImposition = impositionX * impositionY;
      const kaiShuMap: Record<number, string> = {
        1: '全张',
        2: '对开',
        4: '四开',
        8: '八开',
        16: '十六开',
        32: '三十二开'
      };
      return kaiShuMap[totalImposition] || `${totalImposition}开`;
    };

    // 计算拼版方式描述
    const getArrangementDescription = (impositionX: number, impositionY: number) => {
      if (impositionX === impositionY) {
        return `${impositionX}×${impositionY}方形排列`;
      } else {
        return `${impositionX}×${impositionY}矩形排列`;
      }
    };

    return (
      <div style={{ padding: '8px 0' }}>
        <div style={{ marginBottom: '8px' }}>
          <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
            拼版详情信息
          </Text>
        </div>

        <div style={{ marginLeft: '12px' }}>
          <div style={{ marginBottom: '6px' }}>
            <Text style={{ fontSize: '12px' }}>
              <span style={{ color: '#666', fontWeight: 'bold' }}>使用部件：</span>
              <span style={{ color: '#1890ff' }}>{record.partGroups.join('、')}</span>
            </Text>
          </div>

          {record.impositionDetails.map((imposition, index) => (
            <div key={index} style={{
              marginBottom: '8px',
              padding: '6px',
              backgroundColor: '#f9f9f9',
              borderRadius: '4px',
              border: '1px solid #e8e8e8'
            }}>
              <div style={{ marginBottom: '4px' }}>
                <Tag color="blue" style={{ fontSize: '10px' }}>
                  {imposition.partGroupName}
                </Tag>
              </div>

              <Row gutter={[16, 4]}>
                <Col span={12}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>拼版方式：</span>
                    <span style={{ color: '#1890ff' }}>
                      {getArrangementDescription(imposition.impositionX, imposition.impositionY)}
                    </span>
                  </Text>
                </Col>
                <Col span={12}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>开数信息：</span>
                    <span style={{ color: '#52c41a' }}>
                      {getKaiShuInfo(imposition.impositionX, imposition.impositionY)}
                    </span>
                  </Text>
                </Col>
                <Col span={12}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>利用率：</span>
                    <span style={{ color: '#fa8c16' }}>
                      {imposition.efficiency.toFixed(1)}%
                    </span>
                  </Text>
                </Col>
                <Col span={12}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>张数需求：</span>
                    <span style={{ color: '#722ed1' }}>
                      {imposition.sheetsNeeded}张
                    </span>
                  </Text>
                </Col>
                <Col span={24}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>材料尺寸：</span>
                    <span style={{ color: '#8c8c8c' }}>
                      {imposition.materialSize.width.toFixed(0)}×{imposition.materialSize.height.toFixed(0)}mm
                    </span>
                  </Text>
                </Col>
              </Row>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (materialCostDetails.length === 0) {
    return (
      <div>
        <Alert
          message="暂无材料费用明细"
          description="请先完成步骤二的拼版计算，以获取详细的材料使用信息。"
          type="info"
          showIcon
          style={{ marginBottom: 12 }}
        />
        <Text>当前材料费用: {formatCurrency(state.quotation.materialCost)}</Text>
      </div>
    );
  }



  return (
    <div>

      {/* 材料汇总表格 */}
      <Table
        dataSource={materialCostDetails.map((detail, index) => ({
          key: `material-${index}`,
          ...detail
        }))}
        columns={[
          {
            title: '材料类型',
            dataIndex: 'materialType',
            key: 'materialType',
            width: 100,
            align: 'center' as const,
            render: (type: string) => <Tag color="blue">{type}</Tag>
          },
          {
            title: '材料名称',
            dataIndex: 'materialName',
            key: 'materialName',
            width: 150,
            align: 'center' as const,
          },
          {
            title: '规格',
            dataIndex: 'specification',
            key: 'specification',
            width: 80,
            align: 'center' as const
          },
          {
            title: '用量',
            key: 'quantity',
            width: 120,
            align: 'center' as const,
            render: (record: MaterialCostDetail) => (
              <Text strong style={{ color: '#1890ff' }}>
                {record.quantity.toLocaleString()} {getChineseUnit(record.unit)}
              </Text>
            )
          },
          {
            title: '单价',
            dataIndex: 'unitPrice',
            key: 'unitPrice',
            width: 120,
            align: 'center' as const,
            render: (price: number, record: MaterialCostDetail) => (
              <Text type="secondary">
                {formatCurrency(price)}/{getChineseUnit(record.unit)}
              </Text>
            )
          },
          {
            title: '小计',
            dataIndex: 'totalCost',
            key: 'totalCost',
            width: 100,
            align: 'center' as const,
            render: (cost: number) => (
              <Text strong style={{ color: '#52c41a' }}>
                {formatCurrency(cost)}
              </Text>
            )
          }
        ]}
        pagination={false}
        size="small"
        expandable={{
          expandedRowRender: renderMaterialDetails,
          rowExpandable: (record) => !!(record.impositionDetails && record.impositionDetails.length > 0)
        }}
      />

    </div>
  );
};

// 工艺费用详情组件
const ProcessCostDetail: React.FC<{ state: CalculationState }> = ({ state }) => {
  const formatCurrency = (amount: number) => `¥${amount.toLocaleString()}`;

  // 部件组名称映射函数
  const getPartGroupDisplayName = (partGroupKey: string): string => {
    // 尝试从部件组配置中获取中文名称
    const facePartGroups = state.packagingConfig.facePartGroups || [];
    const greyPartGroups = state.packagingConfig.greyPartGroups || [];
    const allPartGroups = [...facePartGroups, ...greyPartGroups];

    const partGroup = allPartGroups.find(pg => pg.id === partGroupKey);
    if (partGroup) {
      return partGroup.name;
    }

    // 如果找不到，尝试从工艺配置中获取
    const processConfig = state.processConfig.partGroupProcessConfigs?.[partGroupKey];
    if (processConfig?.partGroupName) {
      return processConfig.partGroupName;
    }

    // 最后的fallback，将英文key转换为中文
    const keyToChineseMap: Record<string, string> = {
      'face_merged_group': '面纸合并组',
      'grey_merged_group': '灰板合并组',
      'face_group': '面纸组',
      'grey_group': '灰板组'
    };

    return keyToChineseMap[partGroupKey] || partGroupKey;
  };

  // 从工艺配置中提取详细信息
  const processDetails = useMemo(() => {
    const details: Array<{
      partGroup: string;
      processType: string;
      processName: string;
      quantity: number;
      unit: string;
      unitPrice: number;
      totalCost: number;
      description?: string;
      parameters?: Array<{
        label: string;
        value: string;
      }>;
      dimensionInfo?: {
        length?: number;
        width?: number;
        area?: number;
        type: 'hotStamping' | 'embossing' | 'hydraulic' | 'other';
      };
    }> = [];

    if (state.processConfig.partGroupProcessConfigs) {
      Object.entries(state.processConfig.partGroupProcessConfigs).forEach(([partGroupKey, config]) => {
        // 处理各种工艺类型
        const processTypes = ['printing', 'laminating', 'surfaceProcess', 'silkScreen', 'hotStamping', 'embossing', 'dieCutting', 'dieCuttingPlateFee'];
        const processNames: Record<string, string> = {
          printing: '印刷',
          laminating: '对裱',
          surfaceProcess: '覆膜',
          silkScreen: '丝印',
          hotStamping: '烫金',
          embossing: '凹凸',
          dieCutting: '模切',
          dieCuttingPlateFee: '刀版费'
        };

        processTypes.forEach(processType => {
          const configAsAny = config as Record<string, any>;
          const processes = configAsAny[processType];
          if (processes && Array.isArray(processes) && processes.length > 0) {
            processes.forEach((process: Record<string, any>) => {
              // 获取工艺名称和参数
              let processName = '未知工艺';
              let processParameters: Array<{ label: string; value: string }> = [];
              let dimensionInfo: any = undefined;

              // 从计价单位中提取数量单位
              const getQuantityUnit = (unit: string): string => {
                if (unit.includes('元/')) {
                  const quantityUnit = unit.replace('元/', '');
                  return getChineseUnit(quantityUnit);
                }
                return getChineseUnit(unit);
              };

              const originalUnit = process.unit || '个';
              const quantityUnit = getQuantityUnit(originalUnit);

              // 根据工艺类型获取具体名称和参数
              if (processType === 'printing') {
                processName = process.name || process.machineModel || '未知印刷机型';
                if (process.parameters?.machineModel) {
                  processParameters.push({ label: '机型', value: process.parameters.machineModel });
                }
                if (process.parameters?.colorCount && process.parameters.colorCount > 0) {
                  processParameters.push({ label: '颜色数', value: `${process.parameters.colorCount}色` });
                }
                if (process.parameters?.ctpPlateFee && process.parameters.ctpPlateFee > 0) {
                  processParameters.push({ label: 'CTP板费', value: `¥${process.parameters.ctpPlateFee.toFixed(2)}` });
                }
                if (process.parameters?.spotColorFee && process.parameters.spotColorFee > 0) {
                  processParameters.push({ label: '专色费', value: `¥${process.parameters.spotColorFee.toFixed(2)}` });
                }
              } else if (processType === 'hotStamping') {
                processName = process.name || '未知烫金类型';
                if (process.hotStampingDimensions) {
                  const dims = process.hotStampingDimensions;
                  dimensionInfo = {
                    length: dims.length,
                    width: dims.width,
                    area: dims.length * dims.width,
                    type: 'hotStamping' as const
                  };
                  processParameters.push({ label: '烫金尺寸', value: `${dims.length}×${dims.width}mm` });
                  processParameters.push({ label: '烫金面积', value: `${((dims.length * dims.width) / 1000000).toFixed(4)}㎡` });
                }
                if (process.parameters?.materialPrice && process.parameters.materialPrice > 0) {
                  processParameters.push({ label: '材料价格', value: `¥${process.parameters.materialPrice.toFixed(2)}/㎡` });
                }
                if (process.parameters?.salary && process.parameters.salary > 0) {
                  processParameters.push({ label: '工资', value: `¥${process.parameters.salary.toFixed(2)}/张` });
                }
                if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                  processParameters.push({ label: '开机费', value: `¥${process.parameters.setupFee.toFixed(2)}` });
                }
              } else if (processType === 'embossing') {
                processName = process.name || '未知凹凸工艺';
                if (process.embossingDimensions) {
                  const dims = process.embossingDimensions;
                  dimensionInfo = {
                    length: dims.length,
                    width: dims.width,
                    area: dims.length * dims.width,
                    type: 'embossing' as const
                  };
                  processParameters.push({ label: '凹凸尺寸', value: `${dims.length}×${dims.width}mm` });
                  processParameters.push({ label: '凹凸面积', value: `${((dims.length * dims.width) / 1000000).toFixed(4)}㎡` });
                }
                if (process.hydraulicDimensions) {
                  const dims = process.hydraulicDimensions;
                  processParameters.push({ label: '液压尺寸', value: `${dims.length}×${dims.width}mm` });
                  processParameters.push({ label: '液压面积', value: `${((dims.length * dims.width) / 1000000).toFixed(4)}㎡` });
                }
                if (process.parameters?.textureVersion) {
                  processParameters.push({ label: '压纹版', value: process.parameters.textureVersion });
                }
                if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                  processParameters.push({ label: '开机费', value: `¥${process.parameters.setupFee.toFixed(2)}` });
                }
              } else if (processType === 'laminating') {
                processName = process.name || '未知对裱工艺';
                if (process.parameters?.materialType) {
                  processParameters.push({ label: '材料类型', value: process.parameters.materialType });
                }
                if (process.parameters?.thickness) {
                  processParameters.push({ label: '厚度', value: `${process.parameters.thickness}mm` });
                }
                if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                  processParameters.push({ label: '起步价', value: `¥${process.parameters.basePrice.toFixed(2)}` });
                }
              } else if (processType === 'surfaceProcess') {
                processName = process.name || '未知覆膜工艺';
                if (process.parameters?.filmType) {
                  processParameters.push({ label: '膜类型', value: process.parameters.filmType });
                }
                if (process.parameters?.thickness) {
                  processParameters.push({ label: '厚度', value: `${process.parameters.thickness}mm` });
                }
                if (process.parameters?.density) {
                  processParameters.push({ label: '密度', value: process.parameters.density });
                }
                if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                  processParameters.push({ label: '起步价', value: `¥${process.parameters.basePrice.toFixed(2)}` });
                }
              } else if (processType === 'silkScreen') {
                processName = process.name || '未知丝印工艺';
                if (process.parameters?.inkType) {
                  processParameters.push({ label: '油墨类型', value: process.parameters.inkType });
                }
                if (process.parameters?.colorCount && process.parameters.colorCount > 0) {
                  processParameters.push({ label: '颜色数', value: `${process.parameters.colorCount}色` });
                }
                if (process.parameters?.materialFee && process.parameters.materialFee > 0) {
                  processParameters.push({ label: '材料费', value: `¥${process.parameters.materialFee.toFixed(2)}` });
                }
                if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                  processParameters.push({ label: '开机费', value: `¥${process.parameters.setupFee.toFixed(2)}` });
                }
              } else if (processType === 'dieCutting') {
                processName = process.name || '未知模切工艺';
                if (process.parameters?.machineType) {
                  processParameters.push({ label: '机型', value: process.parameters.machineType });
                }
                if (process.parameters?.difficulty) {
                  processParameters.push({ label: '难度', value: process.parameters.difficulty });
                }
                if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                  processParameters.push({ label: '开机费', value: `¥${process.parameters.setupFee.toFixed(2)}` });
                }
              } else if (processType === 'dieCuttingPlateFee') {
                processName = process.name || '未知刀版费';
                if (process.parameters?.plateType) {
                  processParameters.push({ label: '版型', value: process.parameters.plateType });
                }
                if (process.parameters?.area && process.parameters.area > 0) {
                  processParameters.push({ label: '版面积', value: `${process.parameters.area.toFixed(2)}㎡` });
                }
                if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                  processParameters.push({ label: '起步金额', value: `¥${process.parameters.basePrice.toFixed(2)}` });
                }
                if (process.parameters?.impositionQuantity && process.parameters.impositionQuantity > 0) {
                  processParameters.push({ label: '按拼版数量', value: process.parameters.impositionQuantity.toString() });
                }
              } else {
                processName = process.name || process.processName || process.machineModel || processNames[processType];
                // 通用参数提取
                if (process.parameters?.setupFee && process.parameters.setupFee > 0) {
                  processParameters.push({ label: '开机费', value: `¥${process.parameters.setupFee.toFixed(2)}` });
                }
                if (process.parameters?.basePrice && process.parameters.basePrice > 0) {
                  processParameters.push({ label: '起步价', value: `¥${process.parameters.basePrice.toFixed(2)}` });
                }
                if (process.parameters?.materialFee && process.parameters.materialFee > 0) {
                  processParameters.push({ label: '材料费', value: `¥${process.parameters.materialFee.toFixed(2)}` });
                }
              }

              details.push({
                partGroup: getPartGroupDisplayName(partGroupKey),
                processType: processNames[processType],
                processName,
                quantity: process.quantity || 0,
                unit: quantityUnit,
                unitPrice: process.unitPrice || 0,
                totalCost: process.totalPrice || 0,
                description: process.subtype === 'comprehensive' ? '综合计费' : undefined,
                parameters: processParameters.length > 0 ? processParameters : undefined,
                dimensionInfo
              });

              // 刀版费（仅模切工艺）
              if (processType === 'dieCutting' && process.knifeMoldFee && process.knifeMoldFee > 0) {
                details.push({
                  partGroup: getPartGroupDisplayName(partGroupKey),
                  processType: '刀版费',
                  processName: '刀版费',
                  quantity: 1,
                  unit: '个',
                  unitPrice: process.knifeMoldFee,
                  totalCost: process.knifeMoldFee,
                  description: '一次性费用'
                });
              }
            });
          }
        });
      });
    }

    return details;
  }, [state.processConfig.partGroupProcessConfigs, state.packagingConfig.facePartGroups, state.packagingConfig.greyPartGroups]);

  // 渲染工艺详情展开行
  const renderProcessDetails = (record: any) => {
    if (!record.description && !record.parameters && !record.dimensionInfo) {
      return null;
    }

    return (
      <div style={{ padding: '8px 0' }}>
        {record.description && (
          <div style={{ marginBottom: '8px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              计费方式: {record.description}
            </Text>
          </div>
        )}

        {record.parameters && record.parameters.length > 0 && (
          <div style={{ marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
              工艺规格参数:
            </Text>
            <div style={{
              marginLeft: '12px',
              marginTop: '4px',
              padding: '8px',
              backgroundColor: '#f9f9f9',
              borderRadius: '4px',
              border: '1px solid #e8e8e8'
            }}>
              {record.parameters.map((param: { label: string; value: string }, index: number) => (
                <Row key={index} style={{ marginBottom: '4px' }}>
                  <Col span={8}>
                    <Text style={{ fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
                      {param.label}:
                    </Text>
                  </Col>
                  <Col span={16}>
                    <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                      {param.value}
                    </Text>
                  </Col>
                </Row>
              ))}
            </div>
          </div>
        )}

        {record.dimensionInfo && (
          <div>
            <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
              尺寸计算详情:
            </Text>
            <div style={{
              marginLeft: '12px',
              marginTop: '4px',
              padding: '8px',
              backgroundColor: '#f0f8ff',
              borderRadius: '4px',
              border: '1px solid #d9d9d9'
            }}>
              <Space direction="vertical" size="small">
                <Text style={{ fontSize: '12px' }}>
                  长度: {record.dimensionInfo.length.toFixed(1)} mm
                </Text>
                <Text style={{ fontSize: '12px' }}>
                  宽度: {record.dimensionInfo.width.toFixed(1)} mm
                </Text>
                <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                  面积: {record.dimensionInfo.length.toFixed(1)} × {record.dimensionInfo.width.toFixed(1)} = {record.dimensionInfo.area.toFixed(1)} mm²
                </Text>
                <Text style={{ fontSize: '11px', color: '#8c8c8c' }}>
                  ({(record.dimensionInfo.area / 1000000).toFixed(4)} m²)
                </Text>
              </Space>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (processDetails.length === 0) {
    return (
      <div>
        <Text type="secondary">暂无工艺费用明细</Text>
        <br />
        <Text>当前工艺费用: {formatCurrency(state.quotation.processCost)}</Text>
      </div>
    );
  }

  const columns = [
    {
      title: '部件组',
      dataIndex: 'partGroup',
      key: 'partGroup',
      width: 100,
      align: 'center' as const,
      render: (partGroup: string) => <Tag color="orange">{partGroup}</Tag>
    },
    {
      title: '工艺类型',
      dataIndex: 'processType',
      key: 'processType',
      width: 80,
      align: 'center' as const,
      render: (type: string) => <Tag color="purple">{type}</Tag>
    },
    {
      title: '工艺名称',
      dataIndex: 'processName',
      key: 'processName',
      width: 120,
      align: 'center' as const,
      ellipsis: true
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center' as const,
      render: (quantity: number, record: any) => (
        <span>{quantity.toLocaleString()} {record.unit}</span>
      )
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      align: 'center' as const,
      render: (price: number, record: any) => (
        <span>{formatCurrency(price)}/{record.unit}</span>
      )
    },
    {
      title: '小计',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      align: 'center' as const,
      render: (cost: number) => (
        <Text strong style={{ color: '#fa8c16' }}>
          {formatCurrency(cost)}
        </Text>
      )
    }
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={processDetails.map((item, index) => ({ ...item, key: index }))}
        pagination={false}
        size="small"
        expandable={{
          expandedRowRender: renderProcessDetails,
          rowExpandable: (record) => !!(record.description || record.parameters || record.dimensionInfo),
        }}
      />
    </div>
  );
};

// 配件费用详情组件
const AccessoryCostDetail: React.FC<{ state: CalculationState }> = ({ state }) => {
  const formatCurrency = (amount: number) => `¥${amount.toLocaleString()}`;

  // 从配件配置中提取详细信息
  const accessoryDetails = useMemo(() => {
    const details: Array<{
      type: string;
      name: string;
      unit: string;
      quantity: number;
      unitPrice: number;
      totalCost: number;
      description?: string;
      parameters?: Array<{
        label: string;
        value: string;
      }>;
      calculationMethod?: string;
    }> = [];

    // 从计价单位中提取数量单位的函数
    const getAccessoryQuantityUnit = (unit: string): string => {
      if (unit.includes('元/')) {
        const quantityUnit = unit.replace('元/', '');
        return getChineseUnit(quantityUnit);
      }
      return getChineseUnit(unit);
    };

    // 普通配件
    if (state.accessoryConfig.accessories && state.accessoryConfig.accessories.length > 0) {
      state.accessoryConfig.accessories.forEach(accessory => {
        // 使用正确的类型定义
        const accessoryItem = accessory as any;

        // 优先使用materialName，然后是name，最后是fallback
        let accessoryName = '未知配件';
        if (accessoryItem.materialName) {
          accessoryName = accessoryItem.materialName;
        } else if (accessoryItem.name) {
          accessoryName = accessoryItem.name;
        } else if (accessoryItem.material?.name) {
          accessoryName = accessoryItem.material.name;
        } else if (accessoryItem.material?.materialName) {
          accessoryName = accessoryItem.material.materialName;
        } else if (accessoryItem.processName) {
          accessoryName = accessoryItem.processName;
        }

        // 如果还是未知配件，尝试从其他字段获取
        if (accessoryName === '未知配件') {
          console.log('配件数据结构:', accessoryItem);
        }

        const originalUnit = accessoryItem.unit || '个';
        const quantityUnit = getAccessoryQuantityUnit(originalUnit);

        // 提取配件参数
        let accessoryParameters: Array<{ label: string; value: string }> = [];
        if (accessoryItem.parameters) {
          const params = accessoryItem.parameters;

          // 根据参数类型提取相关信息
          if (params.length && params.width) {
            accessoryParameters.push({ label: '规格尺寸', value: `${params.length}×${params.width}mm` });
          }
          if (params.metersPerBox) {
            accessoryParameters.push({ label: '每盒米数', value: `${params.metersPerBox}米` });
          }
          if (params.thickness) {
            accessoryParameters.push({ label: '厚度', value: `${params.thickness}mm` });
          }
          if (params.materialType) {
            accessoryParameters.push({ label: '材料类型', value: params.materialType });
          }
          if (params.color) {
            accessoryParameters.push({ label: '颜色', value: params.color });
          }
          if (params.specification) {
            accessoryParameters.push({ label: '规格说明', value: params.specification });
          }
        }

        details.push({
          type: '普通配件',
          name: accessoryName,
          unit: quantityUnit,
          quantity: accessoryItem.quantity || 0,
          unitPrice: accessoryItem.unitPrice || 0,
          totalCost: accessoryItem.totalPrice || 0,
          description: accessoryItem.parameters ? '查看计算参数' : undefined,
          parameters: accessoryParameters.length > 0 ? accessoryParameters : undefined,
          calculationMethod: '按数量计费'
        });
      });
    }

    // 礼盒配件
    if (state.accessoryConfig.giftBoxAccessories && state.accessoryConfig.giftBoxAccessories.length > 0) {
      state.accessoryConfig.giftBoxAccessories.forEach(giftBoxAccessory => {
        // 使用正确的类型定义
        const giftBoxAccessoryItem = giftBoxAccessory as any;

        // 优先使用materialName，然后是name，最后是fallback
        let accessoryName = '未知配件';
        if (giftBoxAccessoryItem.materialName) {
          accessoryName = giftBoxAccessoryItem.materialName;
        } else if (giftBoxAccessoryItem.name) {
          accessoryName = giftBoxAccessoryItem.name;
        } else if (giftBoxAccessoryItem.material?.name) {
          accessoryName = giftBoxAccessoryItem.material.name;
        } else if (giftBoxAccessoryItem.material?.materialName) {
          accessoryName = giftBoxAccessoryItem.material.materialName;
        } else if (giftBoxAccessoryItem.processName) {
          accessoryName = giftBoxAccessoryItem.processName;
        }

        // 如果还是未知配件，尝试从其他字段获取
        if (accessoryName === '未知配件') {
          console.log('礼盒配件数据结构:', giftBoxAccessoryItem);
        }

        const originalUnit = giftBoxAccessoryItem.unit || '个';
        const quantityUnit = getAccessoryQuantityUnit(originalUnit);

        // 提取礼盒配件参数
        let giftBoxParameters: Array<{ label: string; value: string }> = [];
        let calculationMethodText = '按数量计费';

        // 根据单位推断计算方式
        const unit = giftBoxAccessoryItem.unit || '个';
        if (unit === '元/立方') {
          calculationMethodText = '按体积计算';

          // 显示计算数量（体积）
          if (giftBoxAccessoryItem.quantity) {
            giftBoxParameters.push({
              label: '材料体积',
              value: `${giftBoxAccessoryItem.quantity.toFixed(4)}立方米`
            });
          }

          // 显示库存尺寸（如果使用现货尺寸）
          if (giftBoxAccessoryItem.material?.isStockSize && giftBoxAccessoryItem.material?.stockLength && giftBoxAccessoryItem.material?.stockWidth) {
            giftBoxParameters.push({
              label: '库存尺寸',
              value: `${giftBoxAccessoryItem.material.stockLength}×${giftBoxAccessoryItem.material.stockWidth}mm`
            });
          }

          // 显示用户输入的盒子尺寸
          if (giftBoxAccessoryItem.parameters) {
            const params = giftBoxAccessoryItem.parameters;
            if (params.length && params.width && params.height) {
              giftBoxParameters.push({
                label: '盒子尺寸',
                value: `${params.length}×${params.width}×${params.height}mm`
              });
            }
            if (params.spacing) {
              giftBoxParameters.push({
                label: '材料间距',
                value: `${params.spacing}mm`
              });
            }
          }
        } else if (unit === '元/平方') {
          calculationMethodText = '按表面积计算';

          // 显示计算数量（表面积）
          if (giftBoxAccessoryItem.quantity) {
            giftBoxParameters.push({
              label: '表面积',
              value: `${giftBoxAccessoryItem.quantity.toFixed(4)}平方米`
            });
          }

          // 显示用户输入的盒子尺寸
          if (giftBoxAccessoryItem.parameters) {
            const params = giftBoxAccessoryItem.parameters;
            if (params.length && params.width && params.height) {
              giftBoxParameters.push({
                label: '盒子尺寸',
                value: `${params.length}×${params.width}×${params.height}mm`
              });
            }
          }
        }

        // 显示基础配件信息
        if (giftBoxAccessoryItem.material?.isStockSize) {
          giftBoxParameters.push({ label: '使用现货尺寸', value: '是' });
        }

        details.push({
          type: '礼盒配件',
          name: accessoryName,
          unit: quantityUnit,
          quantity: giftBoxAccessoryItem.quantity || 0,
          unitPrice: giftBoxAccessoryItem.unitPrice || 0,
          totalCost: giftBoxAccessoryItem.totalPrice || 0,
          description: giftBoxAccessoryItem.parameters ? '查看计算参数' : undefined,
          parameters: giftBoxParameters.length > 0 ? giftBoxParameters : undefined,
          calculationMethod: calculationMethodText
        });
      });
    }

    return details;
  }, [state.accessoryConfig.accessories, state.accessoryConfig.giftBoxAccessories]);



  // 渲染配件详情展开行
  const renderAccessoryDetails = (record: any) => {
    if (!record.parameters && !record.calculationMethod) {
      return null;
    }

    return (
      <div style={{ padding: '8px 0' }}>
        {record.calculationMethod && (
          <div style={{ marginBottom: '8px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              计费方式: {record.calculationMethod}
            </Text>
          </div>
        )}

        {record.parameters && record.parameters.length > 0 && (
          <div style={{ marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
              配件规格参数:
            </Text>
            <div style={{
              marginLeft: '12px',
              marginTop: '4px',
              padding: '8px',
              backgroundColor: '#f9f9f9',
              borderRadius: '4px',
              border: '1px solid #e8e8e8'
            }}>
              {record.parameters.map((param: { label: string; value: string }, index: number) => (
                <Row key={index} style={{ marginBottom: '4px' }}>
                  <Col span={8}>
                    <Text style={{ fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
                      {param.label}:
                    </Text>
                  </Col>
                  <Col span={16}>
                    <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                      {param.value}
                    </Text>
                  </Col>
                </Row>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (accessoryDetails.length === 0) {
    return (
      <div>
        <Text type="secondary">暂无配件费用明细</Text>
        <br />
        <Text>当前配件费用: {formatCurrency(state.quotation.accessoryCost)}</Text>
      </div>
    );
  }

  const columns = [
    {
      title: '配件类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      align: 'center' as const,
      render: (type: string) => (
        <Tag color={type === '普通配件' ? 'cyan' : 'magenta'}>{type}</Tag>
      )
    },
    {
      title: '配件名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center' as const,
      ellipsis: true
    },
    {
      title: '数量',
      key: 'quantity',
      width: 120,
      align: 'center' as const,
      render: (record: { quantity: number; unit: string }) => `${record.quantity.toLocaleString()} ${record.unit}`
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 120,
      align: 'center' as const,
      render: (price: number, record: { unit: string }) => `${formatCurrency(price)}/${record.unit}`
    },
    {
      title: '小计',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      align: 'center' as const,
      render: (cost: number) => (
        <Text strong style={{ color: '#722ed1' }}>
          {formatCurrency(cost)}
        </Text>
      )
    }
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={accessoryDetails.map((item, index) => ({ ...item, key: index }))}
        pagination={false}
        size="small"
        expandable={{
          expandedRowRender: renderAccessoryDetails,
          rowExpandable: (record) => !!(record.parameters || record.calculationMethod),
        }}
      />
    </div>
  );
};

// 自定义公式费用详情组件
const FormulaCostDetail: React.FC<{
  formulaResults: FormulaCalculationResult[];
  onRemoveFormula: (index: number) => void;
}> = ({ formulaResults, onRemoveFormula }) => {
  const formatCurrency = (amount: number) => `¥${amount.toLocaleString()}`;

  // 渲染公式详情展开行
  const renderFormulaDetails = (record: FormulaCalculationResult & { index: number }) => {
    return (
      <div style={{ padding: '8px 0' }}>
        <div style={{ marginBottom: '8px' }}>
          <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
            计算详情
          </Text>
        </div>

        <div style={{ marginLeft: '12px' }}>
          {/* 公式表达式 */}
          <div style={{ marginBottom: '8px' }}>
            <Text style={{ fontSize: '12px' }}>
              <span style={{ color: '#666', fontWeight: 'bold' }}>计算表达式：</span>
              <span style={{ color: '#1890ff', fontFamily: 'monospace' }}>
                {record.formula.expression || '无表达式'}
              </span>
            </Text>
          </div>

          {/* 输入参数详情 */}
          <div style={{ marginBottom: '8px' }}>
            <Text style={{ fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
              输入参数：
            </Text>
            <div style={{
              marginTop: '4px',
              padding: '6px',
              backgroundColor: '#f9f9f9',
              borderRadius: '4px',
              border: '1px solid #e8e8e8'
            }}>
              <Row gutter={[16, 4]}>
                {Object.entries(record.inputValues).map(([paramName, value]) => (
                  <Col span={8} key={paramName}>
                    <Text style={{ fontSize: '11px' }}>
                      <span style={{ color: '#666' }}>{paramName}：</span>
                      <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                        {typeof value === 'number' ? value.toLocaleString() : value}
                      </span>
                    </Text>
                  </Col>
                ))}
              </Row>
            </div>
          </div>

          {/* 计算过程 */}
          <div style={{ marginBottom: '8px' }}>
            <Text style={{ fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
              计算过程：
            </Text>
            <div style={{
              marginTop: '4px',
              padding: '6px',
              backgroundColor: '#f0f8ff',
              borderRadius: '4px',
              border: '1px solid #d6e4ff'
            }}>
              <Row gutter={[16, 4]}>
                <Col span={12}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>计算结果：</span>
                    <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                      {formatCurrency(record.calculatedValue)}
                    </span>
                  </Text>
                </Col>
                <Col span={12}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>起步金额：</span>
                    <span style={{ color: '#fa8c16', fontWeight: 'bold' }}>
                      {formatCurrency(record.formula.initialAmount || 0)}
                    </span>
                  </Text>
                </Col>
                <Col span={24}>
                  <Text style={{ fontSize: '11px' }}>
                    <span style={{ color: '#666' }}>最终费用：</span>
                    <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                      {formatCurrency(record.finalCost)}
                    </span>
                    <span style={{ color: '#8c8c8c', marginLeft: '8px' }}>
                      (取计算结果与起步金额的较大值)
                    </span>
                  </Text>
                </Col>
              </Row>
            </div>
          </div>

          {/* 错误信息 */}
          {record.error && (
            <div style={{ marginBottom: '8px' }}>
              <Alert
                message="计算错误"
                description={record.error}
                type="error"
                showIcon
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  if (formulaResults.length === 0) {
    return (
      <div>
        <Alert
          message="暂无自定义费用"
          description="点击上方「新增」按钮添加自定义公式来计算额外费用。"
          type="info"
          showIcon
          style={{ marginBottom: 12 }}
        />
      </div>
    );
  }

  return (
    <div>
      {/* 公式费用汇总表格 */}
      <Table
        dataSource={formulaResults.map((result, index) => ({
          key: `formula-${index}`,
          index,
          ...result
        }))}
        columns={[
          {
            title: '公式名称',
            dataIndex: ['formula', 'name'],
            key: 'formulaName',
            width: 150,
            align: 'center' as const,
            render: (name: string) => (
              <Text strong style={{ color: '#1890ff' }}>{name}</Text>
            )
          },
          {
            title: '费用',
            dataIndex: 'finalCost',
            key: 'finalCost',
            width: 100,
            align: 'center' as const,
            render: (cost: number) => (
              <Text strong style={{ color: '#1890ff' }}>
                {formatCurrency(cost)}
              </Text>
            )
          },
          {
            title: '操作',
            key: 'action',
            width: 80,
            align: 'center' as const,
            render: (record: FormulaCalculationResult & { index: number }) => (
              <Tooltip title="删除此公式">
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onRemoveFormula(record.index)}
                  danger
                />
              </Tooltip>
            )
          }
        ]}
        pagination={false}
        size="small"
        expandable={{
          expandedRowRender: renderFormulaDetails,
          rowExpandable: () => true,
          expandIcon: ({ expanded, onExpand, record }) => (
            <Button
              type="text"
              size="small"
              icon={expanded ? <InfoCircleOutlined /> : <InfoCircleOutlined />}
              onClick={e => onExpand(record, e)}
              style={{
                color: expanded ? '#1890ff' : '#8c8c8c',
                fontSize: '12px'
              }}
            />
          )
        }}
      />
    </div>
  );
};

// 加工费费用详情组件
const ProcessingFeeCostDetail: React.FC<{ state: CalculationState }> = ({ state }) => {
  const formatCurrency = (amount: number) => `¥${amount.toLocaleString()}`;

  // 从加工费配置中提取详细信息
  const processingFeeDetails = useMemo(() => {
    const details: Array<{
      type: string;
      name: string;
      unit: string;
      quantity: number;
      unitPrice: number;
      totalCost: number;
      description?: string;
      parameters?: Array<{
        label: string;
        value: string;
      }>;
      isCustom: boolean;
    }> = [];

    // 可选加工费
    if (state.processingFeeConfig?.customFees) {
      state.processingFeeConfig.customFees.forEach(fee => {
        details.push({
          type: '可选加工费',
          name: fee.name,
          unit: fee.unit,
          quantity: fee.quantity,
          unitPrice: fee.unitPrice,
          totalCost: fee.totalPrice,
          description: fee.remark,
          isCustom: true
        });
      });
    }

    // 固定参数选项加工费
    if (state.processingFeeConfig?.fixedFees) {
      state.processingFeeConfig.fixedFees.forEach(fee => {
        details.push({
          type: '固定参数',
          name: fee.name,
          unit: fee.unit,
          quantity: fee.quantity,
          unitPrice: fee.unitPrice,
          totalCost: fee.totalPrice,
          isCustom: false
        });
      });
    }

    return details;
  }, [state.processingFeeConfig?.customFees, state.processingFeeConfig?.fixedFees]);

  // 渲染加工费详情展开行
  const renderProcessingFeeDetails = (record: any) => {
    return (
      <div style={{ padding: '8px 0' }}>
        {record.description && (
          <div style={{ marginBottom: '8px' }}>
            <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
              备注说明
            </Text>
            <div style={{ marginTop: '4px' }}>
              <Text style={{ fontSize: '12px', color: '#666' }}>
                {record.description}
              </Text>
            </div>
          </div>
        )}

        <div style={{ marginBottom: '8px' }}>
          <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>
            计算详情
          </Text>
          <div style={{ marginTop: '4px' }}>
            <Text style={{ fontSize: '12px', color: '#666' }}>
              计算方式: {record.unitPrice} × {record.quantity} = {formatCurrency(record.unitPrice * record.quantity)}
            </Text>
            {record.totalCost > record.unitPrice * record.quantity && (
              <div>
                <Text style={{ fontSize: '12px', color: '#666' }}>
                  实际费用: {formatCurrency(record.totalCost)} (已应用起步价)
                </Text>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (processingFeeDetails.length === 0) {
    return (
      <div>
        <Alert
          message="暂无加工费费用明细"
          description="请先在加工费选择步骤中添加加工费项目。"
          type="info"
          showIcon
          style={{ marginBottom: 12 }}
        />
        <Text>当前加工费费用: {formatCurrency(state.quotation.processingFeeCost)}</Text>
      </div>
    );
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center' as const,
      render: (quantity: number, record: any) => (
        <Text>{quantity} {record.unit.replace('元/', '')}</Text>
      )
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      align: 'center' as const,
      render: (price: number, record: any) => (
        <span>{formatCurrency(price)}/{record.unit.replace('元/', '')}</span>
      )
    },
    {
      title: '小计',
      dataIndex: 'totalCost',
      key: 'totalCost',
      width: 100,
      align: 'center' as const,
      render: (cost: number) => (
        <Text strong style={{ color: '#17a2b8' }}>
          {formatCurrency(cost)}
        </Text>
      )
    }
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={processingFeeDetails.map((item, index) => ({ ...item, key: index }))}
        pagination={false}
        size="small"
        expandable={{
          expandedRowRender: renderProcessingFeeDetails,
          rowExpandable: (record) => !!(record.description || record.isCustom),
        }}
      />
    </div>
  );
};

export default QuotationStep;