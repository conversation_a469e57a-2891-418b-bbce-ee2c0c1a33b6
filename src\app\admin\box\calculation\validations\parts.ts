import { z } from 'zod';

// 部件公式校验规则
export const partFormulaSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '公式名称不能为空'),
  expression: z.string().optional(),
  isRequired: z.boolean().default(false),
});

// 部件校验规则
export const partSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, '部件名称不能为空').max(50, '部件名称不能超过50个字符'),
  formulas: z.array(partFormulaSchema).optional(),
});

// 部件配置校验规则
export const partConfigSchema = z.object({
  parts: z.array(partSchema).min(1, '至少需要一个部件'),
  mergedParts: z.array(partSchema).optional(),
  totalArea: z.number().min(0).optional(),
});

export type PartFormulaForm = z.infer<typeof partFormulaSchema>;
export type PartForm = z.infer<typeof partSchema>;
export type PartConfigForm = z.infer<typeof partConfigSchema>; 