// 盒型计算流程相关类型定义

import { BoxAttribute, BoxPart, BoxType } from '@/types/box';
import { Paper, SpecialPaper, GreyBoard } from '@/types/material';
import { CustomFormula } from '@/types/customFormula';
import { MaterialTypeAnalysis, PartGroup, ImpositionResult} from './packaging';

// 计算步骤枚举
export enum CalculationStep {
  BASIC_INFO = 'basic_info',        // 基础信息
  PACKAGING = 'packaging',          // 部件合并与材料选择
  PROCESS = 'process',              // 工艺选择
  ACCESSORY = 'accessory',          // 配件选择
  PROCESSING_FEE = 'processing_fee', // 加工选择
  QUOTATION = 'quotation'           // 确认报价（集成自定义公式功能）
}

// 盒子基础信息
export interface BoxBasicInfo {
  id?: number;
  name?: string;               // 计算项目名称
  quantity: number;            // 盒子数量
  attributes?: BoxAttribute[]; // 盒子属性（动态属性）
  boxType?: BoxType;           // 盒子类型
  parts?: BoxPart[];           // 部件信息
}

// 部件配置信息
export interface PartConfig {
  parts: BoxPart[];            // 部件列表
  mergedParts?: BoxPart[];     // 合并后的部件
  totalArea?: number;          // 总展开面积
}

// 部件合并组
export interface PartMergeGroup {
  id: string;                  // 合并组ID
  name: string;                // 合并组名称
  materialType: 'face_paper' | 'grey_board'; // 材料类型
  partIds: number[];           // 包含的部件ID列表
  partNames: string[];         // 包含的部件名称列表
  enabled: boolean;            // 是否启用此合并组
  description?: string;        // 合并组描述
}

// 材料费用详情接口
export interface MaterialCostDetail {
  materialType: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalCost: number;
  sheetsNeeded: number;
  partGroups: string[];
  impositionDetails?: Array<{
    partGroupName: string;
    impositionX: number;
    impositionY: number;
    totalImposition: number;
    efficiency: number;
    sheetsNeeded: number;
    materialSize: { width: number; height: number };
  }>;
}

// 拼版配置信息
export interface PackagingConfig {
  marginLength: number;            // 长度留边(mm)
  marginWidth: number;             // 宽度留边(mm)

  // 打印机相关配置
  enableMaxImposition?: boolean;    // 启用最大拼版
  arrangementMode?: 'normal' | 'cross'; // 排列方式：不交叉/交叉
  enableOptimization?: boolean;     // 启用智能拼版优化

  // 部件合并控制
  enablePartMerging?: boolean;      // 启用部件合并功能
  selectedMergeGroups?: PartMergeGroup[]; // 用户选择的合并组

  // 损耗率配置参数
  paperWasteRate?: number;         // 面纸损耗率（默认10%）
  specialPaperWasteRate?: number;  // 特种纸损耗率（默认5%）
  greyBoardWasteRate?: number;     // 灰板损耗率（默认8%）

  // 新增字段
  materialAnalysis?: MaterialTypeAnalysis;  // 材料类型分析结果
  facePartGroups?: PartGroup[];            // 面纸部件组
  greyPartGroups?: PartGroup[];            // 灰板纸部件组
  faceImpositionResults?: ImpositionResult[]; // 面纸拼版结果
  greyImpositionResults?: ImpositionResult[]; // 灰板纸拼版结果

  // 材料费用相关
  materialCost?: number;                   // 材料总费用
  materialCostDetails?: MaterialCostDetail[]; // 材料费用详情

  // 固定属性值（从BasicInfo中获取）
  bleed?: number;           // 出血
  gripper?: number;         // 拉规
  bite?: number;            // 咬口

  // 计算状态标记
  hasCalculatedImposition?: boolean; // 是否已执行拼版计算


}

// 部件材料配置接口
export interface PartMaterialConfig {
  partGroupId: string;
  // 第一步：选择品类
  materialCategory?: 'paper' | 'specialPaper' | 'greyBoard' | 'corrugated';
  // 第二步：选择具体材料
  materialId?: number;
  materialName?: string;
  // 第三步：选择规格
  materialSpec?: string;
  specOptions?: Array<{
    value: string;
    label: string;
    size?: { width: number; height: number };
    available: boolean;
    price?: number;
  }>;
  // 第四步：显示尺寸
  materialSize?: { width: number; height: number };
  // 打印机配置
  printingMachineId?: number | null;
  printingMachineName?: string;
  printingMachineMaxLength?: number;
  printingMachineMaxWidth?: number;

  // 瓦楞材料专用字段
  facePaper?: string;        // 面纸
  linerPaper?: string;       // 里纸
  structure?: string;        // 结构（三层B/E、三层A/C、五层AB/BC、五层EB、七层EBA）
  structurePrice?: number;   // 结构价格
}

// 材料选择信息
export interface MaterialConfig {
  papers?: SelectedMaterial<Paper>[];           // 选择的纸张
  specialPapers?: SelectedMaterial<SpecialPaper>[]; // 选择的特种纸
  greyBoards?: SelectedMaterial<GreyBoard>[];   // 选择的灰板
  partMaterialConfigs?: Record<string, PartMaterialConfig>;   // 部件材料配置
}

// 选择的材料接口
export interface SelectedMaterial<T> {
  material: T;                 // 材料信息
  quantity: number;            // 使用数量
  unit: string;                // 单位
  unitPrice: number;           // 单价
  totalPrice: number;          // 总价
}

// 部件组工艺配置
export interface PartGroupProcessConfig {
  partGroupId: string;           // 部件组ID
  partGroupName: string;         // 部件组名称
  materialType: 'face' | 'grey'; // 材料类型
  printing?: ProcessItem[];      // 印刷工艺（支持多选）
  surfaceProcess?: ProcessItem[]; // 覆膜工艺（支持多选）
  silkScreen?: ProcessItem[];    // 丝网印刷（支持多选）
  hotStamping?: ProcessItem[];   // 烫金工艺（支持多选）
  laminating?: ProcessItem[];    // 覆膜工艺（支持多选）
  embossing?: ProcessItem[];     // 凹凸工艺（支持多选）
  corrugated?: ProcessItem[];    // 瓦楞工艺（支持多选）
  dieCutting?: ProcessItem[];    // 模切工艺（支持多选）
  dieCuttingPlateFee?: ProcessItem[]; // 刀版费（支持多选）
  groupProcessCost?: number;     // 该部件组工艺总成本
}

// 工艺选择信息 - 按部件分组
export interface ProcessConfig {
  partGroupProcessConfigs?: Record<string, PartGroupProcessConfig>; // 按部件组的工艺配置
  processCost?: number;        // 工艺总成本
}

// 工艺项目接口
export interface ProcessItem {
  id: number;
  name: string;
  quantity: number;            // 数量
  unit: string;                // 单位
  unitPrice: number;           // 单价
  totalPrice: number;          // 总价
  parameters?: Record<string, any>; // 工艺参数
  hotStampingDimensions?: {    // 烫金工艺专用尺寸参数
    length: number;            // 长度(mm)
    width: number;             // 宽度(mm)
  };
  embossingDimensions?: {      // 凹凸工艺专用尺寸参数
    length: number;            // 长度(mm)
    width: number;             // 宽度(mm)
  };
  hydraulicDimensions?: {      // 液压工艺专用尺寸参数
    length: number;            // 长度(mm)
    width: number;             // 宽度(mm)
  };
}

// 配件选择项目接口
export interface AccessoryItem {
  id: number;
  name: string;
  materialName?: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  parameters?: Record<string, any>;
}

// 配件选择信息
export interface AccessoryConfig {
  accessories?: AccessoryItem[];           // 普通配件
  giftBoxAccessories?: AccessoryItem[];    // 礼盒配件
  accessoryCost?: number;                  // 配件总成本
}

// 加工费选择项目接口
export interface ProcessingFeeItem {
  id: number;
  name: string;
  unitPrice: number;
  unit: string;
  basePrice: number;
  quantity: number;
  totalPrice: number;
  remark?: string;
  isCustom?: boolean;                      // 是否为自定义项目
  parameters?: Record<string, any>;        // 自定义参数
}

// 加工费选择信息
export interface ProcessingFeeConfig {
  customFees?: ProcessingFeeItem[];        // 可选加工费（从后台加工费列表中选择）
  fixedFees?: ProcessingFeeItem[];         // 固定参数选项加工费（3个预设项目：开槽、喷码费用、检验费用）
  processingFeeCost?: number;              // 加工费总成本
}

// 公式计算结果接口
export interface FormulaCalculationResult {
  formula: CustomFormula;
  inputValues: Record<string, number>;
  calculatedValue: number;
  finalCost: number;
  error?: string;
}

// 自定义公式配置
export interface FormulaConfig {
  formulas?: CustomFormula[];  // 自定义公式
  formulaResults?: FormulaCalculationResult[]; // 公式计算结果
  additionalFees?: AdditionalFee[]; // 附加费用
  formulaCost?: number;        // 公式计算总成本
}

// 附加费用接口
export interface AdditionalFee {
  id?: number;
  name: string;                // 费用名称
  amount: number;              // 费用金额
  description?: string;        // 费用描述
}

// 报价明细
export interface QuotationDetail {
  basicInfo: BoxBasicInfo;     // 基础信息
  materialCost: number;        // 材料费用
  processCost: number;         // 工艺费用
  accessoryCost: number;       // 配件费用
  processingFeeCost: number;   // 加工费费用
  formulaCost: number;         // 公式费用
  totalCost: number;           // 总费用
  createdAt?: string;          // 创建时间
  updatedAt?: string;          // 更新时间
}

// 完整的计算状态
export interface CalculationState {
  currentStep: CalculationStep;     // 当前步骤
  basicInfo: BoxBasicInfo;          // 基础信息
  partConfig: PartConfig;           // 部件配置
  packagingConfig: PackagingConfig; // 拼版配置
  materialConfig: MaterialConfig;   // 材料配置
  processConfig: ProcessConfig;     // 工艺配置
  accessoryConfig: AccessoryConfig; // 配件配置
  processingFeeConfig: ProcessingFeeConfig; // 加工费配置
  formulaConfig: FormulaConfig;     // 公式配置
  quotation: QuotationDetail;       // 报价明细
  isCalculating: boolean;           // 是否正在计算
  errors: Record<string, string>;   // 错误信息
}

// 计算引擎接口
export interface CalculationEngine {
  calculateMaterialCost: (materialConfig: MaterialConfig, packagingConfig: PackagingConfig) => Promise<number>;
  calculateProcessCost: (processConfig: ProcessConfig, packagingConfig: PackagingConfig) => Promise<number>;
  calculateAccessoryCost: (accessoryConfig: AccessoryConfig) => Promise<number>;
  calculateFormulaCost: (formulaConfig: FormulaConfig, state: CalculationState) => Promise<number>;
  generateQuotation: (state: CalculationState) => Promise<QuotationDetail>;
}

// 步骤导航接口
export interface StepNavigation {
  canGoNext: boolean;          // 可否进入下一步
  canGoPrevious: boolean;      // 可否返回上一步
  nextStep: () => void;        // 下一步操作
  previousStep: () => void;    // 上一步操作
  goToStep: (step: CalculationStep) => void; // 跳转到指定步骤
}
