'use client';

import React, { useState, useEffect } from 'react';
import { 
  Typo<PERSON>, 
  <PERSON>, 
  Space, 
  Breadcrumb,
  Button,
  Tag,
  Empty,
  Descriptions,
  Image,
  Spin,
  Alert,
  Row,
  Col,
  App,
  Badge,
  Divider
} from 'antd';
import { 
  ArrowLeftOutlined,
  EditOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  FormOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Box, BoxAttribute, BoxStatus, BoxType } from '@/types/box';
import { boxApi } from '@/services/adminApi';
import { useErrorHandler, useAsyncError } from '@/lib/hooks/useErrorHandler';

const { Title, Text } = Typography;

// 添加到文件顶部的样式
const styles = {
  imageContainer: `
    .image-container {
      width: 180px;
      margin-bottom: 16px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      padding: 8px;
      background-color: #fafafa;
      transition: all 0.3s;
    }
    .image-container:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .image-preview {
      height: 180px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 2px;
    }
    .image-title {
      margin-top: 8px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  `
};

// 属性显示组件

export default function BoxDetailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const boxId = searchParams.get('id');
  const { clearError, errorState } = useErrorHandler();
  const { execute, loading: asyncLoading } = useAsyncError();
  
  const [boxData, setBoxData] = useState<Box | null>(null);
  const { message: messageApi } = App.useApp();

  // 添加缩放和全屏状态
  const [scale, setScale] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 获取盒型详情
  useEffect(() => {
    if (!boxId) {
      messageApi.error('缺少盒型ID参数');
      router.push('/admin/box');
      return;
    }

    const fetchBoxDetail = async () => {
      const result = await execute(async () => {
        return await boxApi.getDetail(parseInt(boxId));
      }, '获取盒型详情');

      if (result) {
        setBoxData(result);
      } else {
        router.push('/admin/box');
      }
    };

    fetchBoxDetail();
  }, [boxId, router, messageApi]);

  // 获取盒型状态标签颜色

  // 获取盒型状态显示文本

  // 获取盒型类型标签颜色
  const getBoxTypeTagColor = (boxType?: BoxType) => {
    return boxType === BoxType.PAPER_BOX ? 'blue' : 'purple';
  };

  // 获取盒型类型显示文本
  const getBoxTypeText = (boxType?: BoxType) => {
    return boxType === BoxType.PAPER_BOX ? '纸盒' : '精装盒';
  };

  // 处理缩放

  // 处理全屏切换

  // 添加样式到页面
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = styles.imageContainer;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Breadcrumb 
          items={[
            { title: <Link href="/admin/box">盒型管理</Link> },
            { title: '盒型详情' },
          ]}
        />
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 16 }}>
          <Title level={2} style={{ margin: 0 }}>
            <Space>
              <InfoCircleOutlined />
              盒型详情：{boxData?.name}
              {boxData?.boxType && (
                <Tag color={getBoxTypeTagColor(boxData.boxType)} style={{ marginLeft: 8 }}>
                  {getBoxTypeText(boxData.boxType)}
                </Tag>
              )}
            </Space>
          </Title>
          <Space>
            {boxData && (
              <Link href={`/admin/box/edit?id=${boxId}`}>
                <Button type="primary" icon={<EditOutlined />}>编辑盒型</Button>
              </Link>
            )}
            <Link href="/admin/box">
              <Button icon={<ArrowLeftOutlined />}>返回列表</Button>
            </Link>
          </Space>
        </div>
      </div>

      {/* 错误状态显示 */}
      {errorState.hasError && (
        <Alert
          message="操作失败"
          description={errorState.error?.message}
          type="error"
          showIcon
          closable
          onClose={clearError}
          action={
            <Button size="small" icon={<ReloadOutlined />} onClick={() => clearError()}>
              重试
            </Button>
          }
          style={{ marginBottom: 16 }}
        />
      )}

      {asyncLoading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px', flexDirection: 'column' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: '#666' }}>加载中...</div>
        </div>
      ) : boxData ? (
        <div>
          {/* 基本信息卡片 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <SettingOutlined />
                    <span>基本信息</span>
                  </Space>
                }
              >
                <Descriptions column={5}>
                  <Descriptions.Item label="盒型名称" span={1}>
                    {boxData.name}
                  </Descriptions.Item>
                  <Descriptions.Item label="状态" span={1}>
                    <Badge
                      status={boxData.status === BoxStatus.PUBLISHED ? 'success' : 'default'}
                      text={boxData.status === BoxStatus.PUBLISHED ? '已发布' : '草稿'}
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="加工费" span={1}>
                    {boxData.processingFee !== null ? `¥${boxData.processingFee}` : '未设置'}
                  </Descriptions.Item>
                  <Descriptions.Item label="加工费起步价" span={1}>
                    {boxData.processingBasePrice !== null ? `¥${boxData.processingBasePrice}` : '未设置'}
                  </Descriptions.Item>
                  <Descriptions.Item label="盒型描述" span={1}>
                    {boxData.description || '无'}
                  </Descriptions.Item>
                </Descriptions>

                {/* 盒型图片 */}
                <Divider orientation="left">盒型图片</Divider>
                {boxData.images && boxData.images.length > 0 ? (
                  <Image.PreviewGroup>
                    <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
                      {boxData.images.map((image) => (
                        <div
                          key={image.id}
                          style={{
                            width: 100,
                            height: 100,
                            border: '1px solid #f0f0f0',
                            borderRadius: 4,
                            overflow: 'hidden',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#fafafa'
                          }}
                        >
                          <Image
                            src={boxApi.getImageUrl(image.id!)}
                            alt={image.name}
                            style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                          />
                        </div>
                      ))}
                    </div>
                  </Image.PreviewGroup>
                ) : (
                  <Empty description="暂无图片" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </Col>
          </Row>

          {/* 盒型属性卡片 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <SettingOutlined />
                    <span>盒型属性</span>
                  </Space>
                }
              >
                {boxData.attributes && boxData.attributes.length > 0 ? (
                  <Row gutter={[16, 16]}>
                    {boxData.attributes.map((attr, index) => (
                      <Col key={index} span={6}>
                        <Card size="small">
                          <div style={{ textAlign: 'center' }}>
                            <Text strong>{attr.name}</Text>
                            <div style={{ marginTop: 8 }}>
                              <Tag color="blue">
                                {attr.value !== undefined && attr.value !== null ? 
                                  `默认值: ${attr.value}` : 
                                  '无默认值'
                                }
                              </Tag>
                            </div>
                          </div>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                ) : (
                  <Empty description="暂无属性数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </Col>
          </Row>

          {/* 打包信息卡片 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <FormOutlined />
                    <span>打包信息</span>
                  </Space>
                }
              >
                <Descriptions column={3} bordered>
                  <Descriptions.Item label="长度计算公式">
                    <Text code>{boxData.packaging?.lengthFormula || '未设置'}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="宽度计算公式">
                    <Text code>{boxData.packaging?.widthFormula || '未设置'}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="高度计算公式">
                    <Text code>{boxData.packaging?.heightFormula || '未设置'}</Text>
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </Col>
          </Row>

          {/* 部件和公式卡片 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <FormOutlined />
                    <span>部件和公式</span>
                  </Space>
                }
              >
                {boxData.parts && boxData.parts.length > 0 ? (
                  <Row gutter={[16, 16]}>
                    {boxData.parts.map((part, index) => (
                      <Col key={index} span={12}>
                        <Card
                          size="small"
                          title={part.name}
                        >
                          {part.formulas && part.formulas.length > 0 ? (
                            <div>
                              {part.formulas.map((formula, fIndex) => (
                                <div key={fIndex} style={{ marginBottom: 8 }}>
                                  <Text type="secondary">{formula.name}：</Text>
                                  <Text>{formula.expression || '未设置'}</Text>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <Empty description="暂无公式" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                          )}
                        </Card>
                      </Col>
                    ))}
                  </Row>
                ) : (
                  <Empty description="暂无部件数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Card>
            </Col>
          </Row>
        </div>
      ) : (
        <Alert
          message="无法获取盒型数据"
          description="该盒型可能已被删除或您没有查看权限"
          type="error"
          showIcon
        />
      )}
    </div>
  );
} 