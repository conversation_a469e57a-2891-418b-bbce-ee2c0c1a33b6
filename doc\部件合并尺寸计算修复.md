# 部件合并尺寸计算修复

## 问题描述

用户反馈在盒型计算系统中，实际计算的部件尺寸与显示的拼版尺寸不匹配。具体表现为：

1. 合并面纸组显示：部件数: 2 | 拼版尺寸: 575.0×295.0mm
2. 实际拼版计算中显示的部件尺寸为：288.0 × 328.0 mm
3. 两者数值不一致，造成用户困惑

## 问题根因分析

通过代码分析发现问题出现在部件合并逻辑中：

### 1. 错误的合并尺寸计算

在 `PackagingStep.tsx` 中，部件合并时使用了错误的计算方式：

```typescript
// 错误的计算方式（修复前）
const totalLength = Math.max(...selectedFaceParts.map(p => p.length));
const totalWidth = Math.max(...selectedFaceParts.map(p => p.width));
```

这种方式取的是各部件的最大长度和最大宽度，而不是合并后的实际尺寸。

### 2. 不一致的计算逻辑

`MaterialSelectionControl.tsx` 中的 `calculateOptimalImpositionSize` 函数使用了正确的加法逻辑：

```typescript
// 正确的计算方式
const horizontalWidth = parts.reduce((sum, p) => sum + p.width, 0);
const horizontalLength = Math.max(...parts.map(p => p.length));
```

但实际的部件组创建使用了错误的 `Math.max()` 逻辑。

### 3. 显示术语混淆

界面中将合并后的部件尺寸称为"拼版尺寸"，这在术语上是不准确的。

## 修复方案

### 1. 修正部件合并尺寸计算

**文件：** `src/app/admin/box/calculation/components/steps/PackagingStep.tsx`

修改面纸和灰板纸的合并逻辑，采用横向排列方式：
- 宽度：各部件宽度相加
- 长度：取各部件长度的最大值

```typescript
// 修复后的计算方式
// 横向排列：宽度相加，长度取最大值
const totalWidth = selectedFaceParts.reduce((sum, p) => sum + p.width, 0);
const totalLength = Math.max(...selectedFaceParts.map(p => p.length));
```

### 2. 修正拼版引擎中的合并逻辑

**文件：** `src/app/admin/box/calculation/utils/packagingEngine.ts`

更新 `generateMergeCandidates` 方法中的合并组创建逻辑：

```typescript
// 修复后的合并组创建
const combinedGroup: PartGroup = {
  id: `merged_${group1.id}_${group2.id}`,
  name: `${group1.name}+${group2.name}`,
  parts: [...group1.parts, ...group2.parts],
  // 横向排列：宽度相加，长度取最大值
  totalWidth: group1.totalWidth + group2.totalWidth,
  totalLength: Math.max(group1.totalLength, group2.totalLength),
  totalArea: group1.totalArea + group2.totalArea,
  materialType: group1.materialType
};
```

### 3. 优化显示术语

**文件：** `src/app/admin/box/calculation/components/ImpositionLogicDisplay.tsx`

更新显示标签，使术语更加准确：
- "部件尺寸" → "成品尺寸(含出血)"
- "拼版尺寸" → "材料尺寸"（在详细视图中）
- "材料尺寸" → "拼版尺寸"（在折叠视图中）

**文件：** `src/app/admin/box/calculation/components/MaterialSelectionControl.tsx`

更新合并部件的显示标签：
- "拼版尺寸" → "合并尺寸"

## 修复效果

修复后，系统将正确计算和显示：

1. **合并尺寸**：正确反映多个部件横向排列后的实际尺寸
2. **成品尺寸**：包含出血的单个成品尺寸
3. **拼版尺寸**：包含拼版参数（咬口、拉规等）的最终材料尺寸

## 验证方法

1. 创建包含多个部件的盒型
2. 启用部件合并功能
3. 查看合并后的尺寸计算是否正确
4. 进行拼版计算，验证各尺寸显示是否一致

## 相关文件

- `src/app/admin/box/calculation/components/steps/PackagingStep.tsx`
- `src/app/admin/box/calculation/utils/packagingEngine.ts`
- `src/app/admin/box/calculation/components/ImpositionLogicDisplay.tsx`
- `src/app/admin/box/calculation/components/MaterialSelectionControl.tsx`

## 注意事项

此修复遵循了用户记忆中的要求："合并组件尺寸应该通过将尺寸相加来计算，而不是取最大值"，确保了计算逻辑的一致性和准确性。
