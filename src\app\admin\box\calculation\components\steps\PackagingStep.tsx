'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Card, Form, InputNumber, Row, Col, Button, Alert, Space, Tooltip, Select, Checkbox, Spin } from 'antd';
import { CalculatorOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { perfLog, apiCallTracker } from '@/lib/utils/perfLog';
import { BoxType } from '@/types/box';
import { CalculationState, PackagingConfig, MaterialConfig } from '../../types/calculation';
import { packagingEngine } from '../../utils/packagingEngine';
import ImpositionLogicDisplay from '../ImpositionLogicDisplay';
import PartMergeControl from '../PartMergeControl';
import MaterialSelectionControl from '../MaterialSelectionControl';
import { PartMergeGroup } from '../../types/calculation';
import { PartGroup } from '../../types/packaging';
import { Paper, SpecialPaper, GreyBoard, BoxMaterial } from '@/types/material';
import { paperApi, specialPaperApi, greyBoardApi, materialSizeApi, boxMaterialApi, printingMachineApi } from '@/services/adminApi';

// 常量定义
const ATTRIBUTE_CODES = {
  BLEED: 'chuxue',
  GRIPPER: 'lagui',
  BITE: 'yakou'
} as const;

const DEFAULT_VALUES = {
  BLEED: 4,
  GRIPPER: 10,
  BITE: 15,
  MARGIN_LENGTH: 3,
  MARGIN_WIDTH: 3
} as const;

interface PackagingStepProps {
  state: CalculationState;
  onUpdate: {
    packagingConfig: (data: Partial<PackagingConfig>) => void;
    materialConfig: (data: Partial<MaterialConfig>) => void;
  };
}

const PackagingStep: React.FC<PackagingStepProps> = ({
  state,
  onUpdate,
}) => {
  const [calculating, setCalculating] = useState(false);
  const [materialConfigChanged, setMaterialConfigChanged] = useState(false);

  // 材料数据加载状态
  const [materialDataLoading, setMaterialDataLoading] = useState(false);
  const [materialDataError, setMaterialDataError] = useState<string | null>(null);

  // 集中管理的材料数据状态
  const [materialData, setMaterialData] = useState({
    papers: [] as Paper[],
    specialPapers: [] as SpecialPaper[],
    greyBoards: [] as GreyBoard[],
    boxMaterials: [] as BoxMaterial[],
    printingMachines: [] as any[],
    materialSizes: null as any,
    dataFetched: {
      papers: false,
      specialPapers: false,
      greyBoards: false,
      boxMaterials: false,
      printingMachines: false,
      materialSizes: false
    }
  });

  // 用于跟踪材料配置的实际变化，避免API更新导致的误触发
  const lastMaterialConfigRef = useRef<string>('');

  // 用于跟踪是否已经初始化过部件组，避免重复初始化
  const partGroupsInitializedRef = useRef<boolean>(false);

  // 用于跟踪是否已经初始化过材料数据，避免重复API调用
  const materialDataInitializedRef = useRef<boolean>(false);

  // 生成材料配置唯一标识（提取重复逻辑）
  const generateMaterialConfigKey = useCallback((configs: any) => {
    if (!configs || Object.keys(configs).length === 0) return '';

    return Object.entries(configs)
      .map(([key, config]: [string, any]) => `${key}:${config.materialId}:${config.printingMachineId}:${config.materialSpec}`)
      .sort()
      .join('|');
  }, []);

  // 优化的材料配置变化处理函数
  const handlePartMaterialConfigChange = useCallback((configs: Record<string, any>) => {
    perfLog.debug('PackagingStep 接收到材料配置变化:', {
      configsCount: Object.keys(configs).length,
      configKeys: Object.keys(configs)
    });

    try {
      // 验证配置数据结构
      if (!configs || typeof configs !== 'object') {
        perfLog.warn('无效的材料配置数据:', configs);
        return;
      }

      // 生成新的配置标识，用于检测真实变化
      const newConfigKey = generateMaterialConfigKey(configs);
      const currentConfigKey = state.materialConfig?.partMaterialConfigs
        ? generateMaterialConfigKey(state.materialConfig.partMaterialConfigs)
        : '';

      // 只有在配置真正发生变化时才更新
      if (newConfigKey !== currentConfigKey) {
        perfLog.debug('检测到材料配置真实变化，更新状态');

        // 将部件材料配置存储到materialConfig中
        onUpdate.materialConfig({ partMaterialConfigs: configs });

        // 标记材料配置已变化，需要重新计算拼板
        setMaterialConfigChanged(true);

        // 清除之前的拼板计算结果，但保留材料分析结果
        onUpdate.packagingConfig({
          hasCalculatedImposition: false,
          faceImpositionResults: [],
          greyImpositionResults: []
        });

        perfLog.debug('材料配置更新完成，已标记需要重新计算');
      } else {
        perfLog.debug('材料配置未发生实质变化，跳过更新');
      }
    } catch (error) {
      perfLog.error('处理材料配置变化时出错:', error);
      setMaterialDataError('材料配置更新失败，请重试');
    }
  }, [onUpdate.materialConfig, onUpdate.packagingConfig, generateMaterialConfigKey, state.materialConfig?.partMaterialConfigs]);

  // 获取盒子类型（从BasicInfo中获取，如果有sourceBox的话）
  const getBoxType = useCallback((): BoxType | undefined => {
    return state.basicInfo.boxType;
  }, [state.basicInfo.boxType]);

  // 获取固定属性值
  const getFixedAttributes = useCallback(() => {
    const attributes = state.basicInfo.attributes || [];
    const bleed = attributes.find(attr => attr.code === ATTRIBUTE_CODES.BLEED)?.value || DEFAULT_VALUES.BLEED;
    const gripper = attributes.find(attr => attr.code === ATTRIBUTE_CODES.GRIPPER)?.value || DEFAULT_VALUES.GRIPPER;
    const bite = attributes.find(attr => attr.code === ATTRIBUTE_CODES.BITE)?.value || DEFAULT_VALUES.BITE;

    return { bleed, gripper, bite };
  }, [state.basicInfo.attributes]);

  // 计算部件尺寸（提取重复逻辑）
  const calculatePartDimensions = useCallback(() => {
    if (!state.basicInfo.parts || state.basicInfo.parts.length === 0) {
      return [];
    }

    const attributesMap = state.basicInfo.attributes?.reduce((acc, attr) => {
      acc[attr.code] = attr.value || 0;
      acc[attr.name] = attr.value || 0;
      return acc;
    }, {} as Record<string, number>) || {};

      return packagingEngine.calculatePartDimensions(state.basicInfo.parts, attributesMap);
  }, [state.basicInfo.parts, state.basicInfo.attributes]);

  // 比较部件组是否发生变化（优化性能）
  const comparePartGroups = useCallback((current: any[], updated: any[]) => {
    if (current.length !== updated.length) return true;

    return current.some((group, index) => {
      const updatedGroup = updated[index];
      return group.id !== updatedGroup.id ||
             group.name !== updatedGroup.name ||
             group.parts.length !== updatedGroup.parts.length;
    });
  }, []);

  // 检查是否有拼版结果
  const hasImpositionResults = useMemo(() => {
    return (state.packagingConfig.faceImpositionResults?.length || 0) > 0 ||
           (state.packagingConfig.greyImpositionResults?.length || 0) > 0;
  }, [state.packagingConfig.faceImpositionResults, state.packagingConfig.greyImpositionResults]);

  // 检查材料配置是否完成
  const isMaterialConfigComplete = useMemo(() => {
    const partMaterialConfigs = state.materialConfig?.partMaterialConfigs;
    if (!partMaterialConfigs || Object.keys(partMaterialConfigs).length === 0) {
      return false;
    }

    const facePartGroups = state.packagingConfig.facePartGroups || [];
    const greyPartGroups = state.packagingConfig.greyPartGroups || [];
    const allPartGroups = [...facePartGroups, ...greyPartGroups];

    // 检查每个部件组是否都有完整的材料配置
    for (const group of allPartGroups) {
      const config = partMaterialConfigs[group.id];
      if (!config) {
        perfLog.debug(`部件组 ${group.id} 缺少材料配置`);
        return false;
      }

      // 面纸部件组的验证
      if (group.id.startsWith('face_')) {
        if (!config.materialCategory) {
          perfLog.debug(`面纸部件组 ${group.id} 缺少材料品类`);
          return false;
        }
        if (!config.materialId) {
          perfLog.debug(`面纸部件组 ${group.id} 缺少材料选择`);
          return false;
        }
        if (!config.materialSpec) {
          perfLog.debug(`面纸部件组 ${group.id} 缺少材料规格`);
          return false;
        }
        if (!config.printingMachineId) {
          perfLog.debug(`面纸部件组 ${group.id} 缺少打印机选择`);
          return false;
        }
      }

      // 灰板纸部件组的验证
      if (group.id.startsWith('grey_')) {
        if (!config.materialCategory) {
          perfLog.debug(`灰板纸部件组 ${group.id} 缺少材料品类`);
          return false;
        }

        if (config.materialCategory === 'greyBoard') {
          if (!config.materialId) {
            perfLog.debug(`灰板纸部件组 ${group.id} 缺少材料选择`);
            return false;
          }
          if (!config.materialSpec) {
            perfLog.debug(`灰板纸部件组 ${group.id} 缺少材料规格`);
            return false;
          }
        } else if (config.materialCategory === 'corrugated') {
          if (!config.facePaper) {
            perfLog.debug(`瓦楞部件组 ${group.id} 缺少面纸选择`);
            return false;
          }
          if (!config.linerPaper) {
            perfLog.debug(`瓦楞部件组 ${group.id} 缺少里纸选择`);
            return false;
          }
          if (!config.structure) {
            perfLog.debug(`瓦楞部件组 ${group.id} 缺少结构选择`);
            return false;
          }
        }
      }
    }

    perfLog.debug('所有部件组材料配置验证通过');
    return true;
  }, [
    state.materialConfig?.partMaterialConfigs,
    state.packagingConfig.facePartGroups,
    state.packagingConfig.greyPartGroups
  ]);

  // 创建未合并部件组的工具函数
  const createUnmergedPartGroups = useCallback((
    parts: any[],
    materialType: 'face' | 'grey'
  ) => {
    return parts.map((part, index) => ({
      id: `${materialType}_${part.part.id!.toString()}_${index}`, // 修复：添加索引确保唯一性，避免React key重复
      name: part.part.name,
      parts: [part],
      totalLength: part.length,
      totalWidth: part.width,
      totalArea: part.area,
      materialType: materialType
    }));
  }, []);

  // 获取材料约束的工具函数（提取重复逻辑）
  const getMaterialConstraints = useCallback((partGroupId: string) => {
    const partMaterialConfigs = state.materialConfig?.partMaterialConfigs;
    if (!partMaterialConfigs) {
      return null;
    }

    let groupConfig = partMaterialConfigs[partGroupId];

    // 如果没有找到，尝试查找相关配置
    if (!groupConfig) {
      const allPartGroups = [
        ...(state.packagingConfig.facePartGroups || []),
        ...(state.packagingConfig.greyPartGroups || [])
      ];
      const currentPartGroup = allPartGroups.find(g => g.id === partGroupId);

      if (currentPartGroup) {
        if (partGroupId.includes('face_merged') || partGroupId.startsWith('face_')) {
          const faceConfigKey = Object.keys(partMaterialConfigs).find(key =>
            key.startsWith('face_') || partMaterialConfigs[key]?.materialCategory === 'paper'
          );
          if (faceConfigKey) {
            groupConfig = partMaterialConfigs[faceConfigKey];
          }
        } else if (partGroupId.includes('grey_merged') || partGroupId.startsWith('grey_')) {
          const greyConfigKey = Object.keys(partMaterialConfigs).find(key =>
            key.startsWith('grey_') || partMaterialConfigs[key]?.materialCategory === 'greyBoard'
          );
          if (greyConfigKey) {
            groupConfig = partMaterialConfigs[greyConfigKey];
          }
        }
      }
    }

    if (!groupConfig) {
      return null;
    }

    return {
      materialSize: groupConfig.materialSize,
      printingMachineMaxLength: groupConfig.printingMachineMaxLength,
      printingMachineMaxWidth: groupConfig.printingMachineMaxWidth,
      materialName: groupConfig.materialName,
      materialSpec: groupConfig.materialSpec
    };
  }, [state.materialConfig?.partMaterialConfigs, state.packagingConfig.facePartGroups, state.packagingConfig.greyPartGroups]);

  // 获取材料尺寸配置
  const fetchMaterialSizes = useCallback(async () => {
    const apiKey = 'materialSize-getList';

    // 如果已经有数据，跳过
    if (materialData.materialSizes) {
      perfLog.debug('材料尺寸配置已存在，跳过获取');
      return;
    }

    if (!apiCallTracker.canCall(apiKey)) {
      perfLog.debug('材料尺寸配置API调用被阻止，防止重复调用');
      return;
    }

    try {
      const result = await materialSizeApi.getList();
      if (result.success && result.data) {
        setMaterialData(prev => ({
          ...prev,
          materialSizes: result.data?.config || result.data,
          dataFetched: { ...prev.dataFetched, materialSizes: true }
        }));
        perfLog.debug('材料尺寸配置获取成功:', result.data.config || result.data);
      }
    } catch (error) {
      perfLog.error('获取材料尺寸配置失败:', error);
      throw error
    }
  }, [materialData.materialSizes]);

  // 获取面纸相关数据
  const fetchFacePaperData = useCallback(async () => {
    const promises = [];

    // 获取纸类材料
    if (materialData.papers.length === 0 && apiCallTracker.canCall('paper-getList')) {
      promises.push(
        paperApi.getList({ page: 1, pageSize: 100 }).then(result => {
          const papers = result.success && result.data ? result.data.list : [];
          setMaterialData(prev => ({
            ...prev,
            papers,
            dataFetched: { ...prev.dataFetched, papers: true }
          }));
          perfLog.debug('纸类材料获取成功:', papers.length);
        }).catch(error => {
          perfLog.error('获取纸类材料失败:', error);
        })
      );
    } else if (materialData.papers.length > 0) {
      perfLog.debug('纸类材料已存在，跳过获取');
    }

    // 获取特种纸材料
    if (materialData.specialPapers.length === 0 && apiCallTracker.canCall('specialPaper-getList')) {
      perfLog.debug('开始获取特种纸材料...');
      promises.push(
        specialPaperApi.getList({ page: 1, pageSize: 100 }).then(result => {
          perfLog.debug('特种纸API响应:', {
            success: result.success,
            hasData: !!result.data,
            dataStructure: result.data ? Object.keys(result.data) : null,
            listLength: result.data?.list?.length || 0
          });

          const specialPapers = result.success && result.data ? result.data.list : [];
          setMaterialData(prev => ({
            ...prev,
            specialPapers,
            dataFetched: { ...prev.dataFetched, specialPapers: true }
          }));
          perfLog.debug('特种纸材料获取成功:', {
            count: specialPapers.length,
            items: specialPapers.map(p => ({ id: p.id, name: p.name }))
          });
        }).catch(error => {
          perfLog.error('获取特种纸材料失败:', error);
        })
      );
    } else if (materialData.specialPapers.length > 0) {
      perfLog.debug('特种纸材料已存在，跳过获取');
    } else {
      perfLog.debug('特种纸材料API调用被阻止或条件不满足:', {
        specialPapersLength: materialData.specialPapers.length,
        canCallAPI: apiCallTracker.canCall('specialPaper-getList')
      });
    }

    return Promise.all(promises);
  }, [materialData.papers.length, materialData.specialPapers.length]);

  // 获取灰板纸相关数据
  const fetchGreyBoardData = useCallback(async () => {
    const promises = [];

    // 获取灰板纸材料
    if (materialData.greyBoards.length === 0 && apiCallTracker.canCall('greyBoard-getList')) {
      promises.push(
        greyBoardApi.getList({ page: 1, pageSize: 100 }).then(result => {
          const greyBoards = result.success && result.data ? result.data.list : [];
          setMaterialData(prev => ({
            ...prev,
            greyBoards,
            dataFetched: { ...prev.dataFetched, greyBoards: true }
          }));
          perfLog.debug('灰板纸材料获取成功:', greyBoards.length);
        }).catch(error => {
          perfLog.error('获取灰板纸材料失败:', error);
        })
      );
    } else if (materialData.greyBoards.length > 0) {
      perfLog.debug('灰板纸材料已存在，跳过获取');
    }

    // 获取纸箱材料
    if (materialData.boxMaterials.length === 0 && apiCallTracker.canCall('boxMaterial-getList')) {
      promises.push(
        boxMaterialApi.getList({ page: 1, pageSize: 100 }).then(result => {
          const boxMaterials = result.success && result.data ? result.data.list : [];
          setMaterialData(prev => ({
            ...prev,
            boxMaterials,
            dataFetched: { ...prev.dataFetched, boxMaterials: true }
          }));
          perfLog.debug('纸箱材料获取成功:', boxMaterials.length);
        }).catch(error => {
          perfLog.error('获取纸箱材料失败:', error);
        })
      );
    } else if (materialData.boxMaterials.length > 0) {
      perfLog.debug('纸箱材料已存在，跳过获取');
    }

    return Promise.all(promises);
  }, [materialData.greyBoards.length, materialData.boxMaterials.length]);

  // 获取打印机数据（独立函数，因为打印机数据总是需要的）
  const fetchPrintingMachineData = useCallback(async () => {
    // 获取打印机列表
    if (materialData.printingMachines.length === 0 && apiCallTracker.canCall('printingMachine-getOptions')) {
      try {
        const result = await printingMachineApi.getOptions();
        if (result.success) {
          setMaterialData(prev => ({
            ...prev,
            printingMachines: result.data,
            dataFetched: { ...prev.dataFetched, printingMachines: true }
          }));
          perfLog.debug('打印机列表获取成功:', result.data.length);
        }
      } catch (error) {
        perfLog.error('获取打印机列表失败:', error);
      }
    } else if (materialData.printingMachines.length > 0) {
      perfLog.debug('打印机列表已存在，跳过获取');
    }
  }, [materialData.printingMachines.length]);

  // 集中式材料数据初始化函数
  const initializeMaterialData = useCallback(async (materialAnalysis: any) => {
    if (materialDataInitializedRef.current) {
      perfLog.debug('材料数据已初始化，跳过重复初始化');
      return;
    }

    if (!materialAnalysis) {
      perfLog.debug('没有材料分析结果，跳过材料数据初始化');
      return;
    }

    perfLog.debug('开始集中式材料数据初始化', {
      canSelectFacePaper: materialAnalysis.canSelectFacePaper,
      canSelectGreyBoard: materialAnalysis.canSelectGreyBoard
    });

    try {
      setMaterialDataLoading(true);
      setMaterialDataError(null);

      // 始终获取材料尺寸配置和打印机数据
      const basicPromises = [
        fetchMaterialSizes(),
        fetchPrintingMachineData() // 打印机数据总是需要的
      ];
      await Promise.all(basicPromises);

      // 根据材料分析结果获取对应的材料数据
      const materialPromises = [];

      if (materialAnalysis.canSelectFacePaper) {
        perfLog.debug('开始获取面纸相关数据');
        materialPromises.push(fetchFacePaperData());
      }

      if (materialAnalysis.canSelectGreyBoard) {
        perfLog.debug('开始获取灰板纸相关数据');
        materialPromises.push(fetchGreyBoardData());
      }

      await Promise.all(materialPromises);

      // 标记材料数据已初始化
      materialDataInitializedRef.current = true;
      perfLog.debug('材料数据初始化完成');

    } catch (error) {
      perfLog.error('材料数据初始化失败:', error);
      setMaterialDataError('材料数据加载失败，请刷新页面重试');
    } finally {
      setMaterialDataLoading(false);
    }
  }, [fetchMaterialSizes, fetchPrintingMachineData, fetchFacePaperData, fetchGreyBoardData]);

  // 辅助函数：判断部件是否适用于指定材料类型
  // 使用与packagingEngine相同的逻辑，确保一致性
  const isPartForMaterial = useCallback((part: any, materialType: 'face' | 'grey'): boolean => {
    if (!part.part.formulas) {
      perfLog.debug(`部件 ${part.part.name} 没有公式`);
      return false;
    }

    // 检查公式名称是否包含面纸相关内容
    const containsFacePaperFormulaName = (formulaName: string): boolean => {
      const faceKeywords = ['面纸长度', '面纸宽度', '面纸长', '面纸宽', 'face_length', 'face_width'];
      return faceKeywords.some(keyword => formulaName.includes(keyword.toLowerCase()));
    };

    // 检查公式名称是否包含灰板纸相关内容
    const containsGreyBoardFormulaName = (formulaName: string): boolean => {
      const greyKeywords = ['灰板纸长度', '灰板纸宽度', '灰板长度', '灰板宽度', '灰板长', '灰板宽', 'grey_length', 'grey_width'];
      return greyKeywords.some(keyword => formulaName.includes(keyword.toLowerCase()));
    };

    // 对于面纸材料，先检查是否有明确的面纸公式
    if (materialType === 'face') {
      const hasFacePaperFormula = part.part.formulas.some((formula: any) => {
        if (!formula.expression || !formula.expression.trim()) return false;
        const formulaName = formula.name.toLowerCase();
        return containsFacePaperFormulaName(formulaName);
      });

      // 如果有明确的面纸公式，返回true
      if (hasFacePaperFormula) {
        perfLog.debug(`部件 ${part.part.name} 有明确的面纸公式`);
        return true;
      }

      // 如果没有明确的面纸公式，但也没有灰板纸公式，且有有效表达式，则认为是面纸材料
      // 这个逻辑适用于纸盒等只需要面纸的情况
      const hasGreyBoardFormula = part.part.formulas.some((formula: any) => {
        if (!formula.expression || !formula.expression.trim()) return false;
        const formulaName = formula.name.toLowerCase();
        return containsGreyBoardFormulaName(formulaName);
      });

      const hasValidFormula = part.part.formulas.some((formula: any) =>
        formula.expression && formula.expression.trim()
      );

      if (!hasGreyBoardFormula && hasValidFormula) {
        perfLog.debug(`部件 ${part.part.name} 没有灰板纸公式但有有效公式，认为是面纸材料`);
        return true;
      }

      perfLog.debug(`部件 ${part.part.name} 不匹配面纸材料`);
      return false;
    }

    // 对于灰板纸材料，严格按公式名称匹配
    if (materialType === 'grey') {
      const result = part.part.formulas.some((formula: any) => {
        if (!formula.expression || !formula.expression.trim()) {
          return false;
        }

        const formulaName = formula.name.toLowerCase();
        const isMatch = containsGreyBoardFormulaName(formulaName);
        perfLog.debug(`灰板纸匹配结果: ${isMatch} for ${formula.name}`);
        return isMatch;
      });

      perfLog.debug(`部件 ${part.part.name} 最终匹配 ${materialType} 结果: ${result}`);
      return result;
    }

    return false;
  }, []);

  // 执行拼版计算
  const performPackagingCalculation = useCallback(async () => {
    try {
      setCalculating(true);

      const facePartGroups = state.packagingConfig.facePartGroups || [];
      const greyPartGroups = state.packagingConfig.greyPartGroups || [];

      perfLog.debug('开始拼版计算:', {
        hasAttributes: !!state.basicInfo.attributes,
        attributesLength: state.basicInfo.attributes?.length || 0,
        hasMaterialConfig: !!state.materialConfig,
        materialConfigKeys: state.materialConfig ? Object.keys(state.materialConfig) : [],
        partMaterialConfigs: state.materialConfig?.partMaterialConfigs,
        partMaterialConfigsKeys: state.materialConfig?.partMaterialConfigs ? Object.keys(state.materialConfig.partMaterialConfigs) : [],
        hasMaterialAnalysis: !!state.packagingConfig.materialAnalysis,
        materialAnalysis: state.packagingConfig.materialAnalysis,
        currentFacePartGroups: state.packagingConfig.facePartGroups?.length || 0,
        currentGreyPartGroups: state.packagingConfig.greyPartGroups?.length || 0
      });

      perfLog.debug('使用的部件组:', {
        facePartGroups: facePartGroups.length,
        greyPartGroups: greyPartGroups.length,
        facePartGroupsDetails: facePartGroups.map(g => ({
          id: g.id,
          name: g.name,
          partsCount: g.parts.length,
          totalLength: g.totalLength,
          totalWidth: g.totalWidth
        })),
        greyPartGroupsDetails: greyPartGroups.map(g => ({
          id: g.id,
          name: g.name,
          partsCount: g.parts.length,
          totalLength: g.totalLength,
          totalWidth: g.totalWidth
        }))
      });

      // 验证部件组是否有效
      if (facePartGroups.length === 0 && greyPartGroups.length === 0) {
        perfLog.error('拼版计算失败：没有有效的部件组', {
          materialAnalysis: state.packagingConfig.materialAnalysis,
          basicInfoParts: state.basicInfo.parts?.map(p => ({
            name: p.name,
            formulas: p.formulas?.map(f => ({ name: f.name, expression: f.expression }))
          })),
          partDimensionsCount: calculatePartDimensions().length,
          facePartGroupsLength: facePartGroups.length,
          greyPartGroupsLength: greyPartGroups.length
        });

        // 显示用户友好的错误信息
        setMaterialDataError('拼版计算失败：没有找到有效的部件组。请检查部件公式配置是否正确。');
        return;
      }

      // 获取计算参数
      const { bleed, gripper, bite } = getFixedAttributes();

      const impositionParams = {
        marginLength: state.packagingConfig.marginLength,
        marginWidth: state.packagingConfig.marginWidth,
        bleed,
        gripper,
        bite,
        enableMaxImposition: state.packagingConfig.enableMaxImposition,
        arrangementMode: state.packagingConfig.arrangementMode || 'normal',
        quantity: state.basicInfo.quantity, // 传递盒子数量
        getMaterialConstraints // 传递函数以获取每个部件组的材料约束
      };

      perfLog.debug('拼版参数:', impositionParams);

      // 拼版优化选项
      const optimizationOptions = {
        enableRotation: state.packagingConfig.enableOptimization || false,
        enableGroupMerge: false, // 禁用自动合并，用户通过PartMergeControl手动控制
        enableMixedRotation: false,
        enableDynamicGrouping: false,
        maxGroupSize: 1, // 不允许自动分组
        maxIterations: state.packagingConfig.enableOptimization ? 5 : 1,
        minEfficiencyThreshold: 60,
        allowOversizeGrouping: false,
        prioritizeMaterialSaving: state.packagingConfig.enableOptimization || false,
        optimizeFor: 'efficiency' as const,
        mergeStrategy: {
          preferSimilarSizes: false, // 禁用相似尺寸合并
          allowSizeVariance: 0, // 不允许尺寸变化
          maxPartsPerGroup: 1, // 每组最多1个部件
          minGroupEfficiency: 100 // 要求100%效率，防止自动合并
        },
        scoreWeights: {
          efficiency: 0.6,
          totalImposition: 0.3,
          materialArea: 0.1,
          groupCount: 0.0, // 不考虑组数量
          rotationPenalty: 0.0 // 不惩罚旋转
        }
      };

      perfLog.debug('拼版计算参数:', {
        arrangementMode: impositionParams.arrangementMode,
        enableOptimization: state.packagingConfig.enableOptimization,
        facePartGroupsCount: facePartGroups.length,
        greyPartGroupsCount: greyPartGroups.length,
        optimizationOptions: optimizationOptions
      });

      // 计算面纸拼版（使用优化）
      const faceImpositionResults = facePartGroups.length > 0
        ? packagingEngine.optimizeImposition(facePartGroups, impositionParams, optimizationOptions, state.basicInfo)
        : [];

      // 计算灰板纸拼版（使用优化）
      const greyImpositionResults = greyPartGroups.length > 0
        ? packagingEngine.optimizeImposition(greyPartGroups, impositionParams, optimizationOptions, state.basicInfo)
        : [];

      perfLog.debug('拼版计算结果:', {
        faceResults: faceImpositionResults.length,
        greyResults: greyImpositionResults.length,
        faceEfficiency: faceImpositionResults[0]?.efficiency,
        greyEfficiency: greyImpositionResults[0]?.efficiency
      });

      // 一次性更新所有状态，避免多次重新渲染
      const needsPartGroupUpdate = (
        comparePartGroups(state.packagingConfig.facePartGroups || [], facePartGroups) ||
        comparePartGroups(state.packagingConfig.greyPartGroups || [], greyPartGroups)
      );

      // 合并所有状态更新为一次操作
      onUpdate.packagingConfig({
        faceImpositionResults,
        greyImpositionResults,
        bleed,
        gripper,
        bite,
        hasCalculatedImposition: true,
        materialAnalysis: state.packagingConfig.materialAnalysis,
        // 只有在需要时才更新部件组
        ...(needsPartGroupUpdate ? { facePartGroups, greyPartGroups } : {})
      });

      // 重置材料配置变化标记
      setMaterialConfigChanged(false);

      // 更新材料配置引用，防止后续API更新误触发
      if (state.materialConfig?.partMaterialConfigs) {
        lastMaterialConfigRef.current = generateMaterialConfigKey(state.materialConfig.partMaterialConfigs);
      }

    } catch (error) {
      perfLog.error('拼版计算错误:', error);
    } finally {
      setCalculating(false);
    }
  }, [
    // 移除generateFinalPartGroups依赖，避免循环依赖
    // 函数内部直接调用generateFinalPartGroups，不需要作为依赖
    state.packagingConfig.enablePartMerging,
    state.packagingConfig.selectedMergeGroups,
    state.packagingConfig.marginLength,
    state.packagingConfig.marginWidth,
    state.packagingConfig.enableMaxImposition,
    state.packagingConfig.arrangementMode,
    state.packagingConfig.enableOptimization,
    state.basicInfo.quantity,
    getFixedAttributes,
    getMaterialConstraints,
    generateMaterialConfigKey,
    state.materialConfig?.partMaterialConfigs,
    onUpdate.packagingConfig
  ]);

  // 初始化材料分析和材料数据（确保MaterialSelectionControl能正常显示）
  useEffect(() => {
    const parts = state.basicInfo.parts;
    const boxType = getBoxType();

    perfLog.debug('初始化材料分析useEffect执行', {
      hasParts: !!parts,
      partsLength: parts?.length || 0,
      boxType,
      hasMaterialAnalysis: !!state.packagingConfig.materialAnalysis,
      materialDataInitialized: materialDataInitializedRef.current,
      currentStep: state.currentStep
    });

    // 如果有材料分析结果但材料数据未初始化（步骤切换回来的情况）
    if (state.packagingConfig.materialAnalysis && !materialDataInitializedRef.current) {
      perfLog.debug('检测到材料分析结果存在但材料数据未初始化，重新初始化材料数据');
      initializeMaterialData(state.packagingConfig.materialAnalysis);
      return;
    }

    // 只在没有材料分析结果且未初始化材料数据时进行初始化
    if (parts && parts.length > 0 && !state.packagingConfig.materialAnalysis && !materialDataInitializedRef.current) {
      perfLog.debug('开始初始化材料分析');

      try {
        // 分析材料类型
        const materialAnalysis = packagingEngine.analyzeMaterialTypes(parts, boxType);
        perfLog.debug('材料类型分析结果:', materialAnalysis);

        // 立即更新材料分析结果
        onUpdate.packagingConfig({ materialAnalysis });
        perfLog.debug('材料分析结果已更新到状态');

        // 初始化材料数据
        initializeMaterialData(materialAnalysis);
      } catch (error) {
        perfLog.error('初始化材料分析失败:', error);
        setMaterialDataError('材料分析失败，请检查部件配置');
      }
    } else {
      perfLog.debug('跳过材料分析初始化', {
        reason: !parts ? '没有部件' :
          parts.length === 0 ? '部件数量为0' :
            state.packagingConfig.materialAnalysis ? '已有材料分析结果' :
              '材料数据已初始化'
      });
    }
  }, [state.packagingConfig.materialAnalysis, state.basicInfo.parts, getBoxType, onUpdate.packagingConfig, initializeMaterialData]);

  // 监听材料数据加载状态，确保加载状态正确更新
  useEffect(() => {
    if (!state.packagingConfig.materialAnalysis) {
      return;
    }

    const { canSelectFacePaper, canSelectGreyBoard } = state.packagingConfig.materialAnalysis;

    // 检查是否有足够的材料数据
    const hasFacePaperData = !canSelectFacePaper ||
      (materialData.papers.length > 0 || materialData.specialPapers.length > 0);

    const hasGreyBoardData = !canSelectGreyBoard ||
      (materialData.greyBoards.length > 0 || materialData.boxMaterials.length > 0);

    const hasMaterialSizes = !!materialData.materialSizes;

    // 打印机数据总是需要的
    const hasPrintingMachineData = materialData.printingMachines.length > 0;

    const allDataLoaded = hasFacePaperData && hasGreyBoardData && hasMaterialSizes && hasPrintingMachineData;

    perfLog.debug('材料数据加载状态检查:', {
      canSelectFacePaper,
      canSelectGreyBoard,
      hasFacePaperData,
      hasGreyBoardData,
      hasMaterialSizes,
      hasPrintingMachineData,
      allDataLoaded,
      currentLoadingState: materialDataLoading,
      materialDataInitialized: materialDataInitializedRef.current,
      printingMachinesCount: materialData.printingMachines.length
    });

    // 如果所有需要的数据都已加载，且当前处于加载状态，则关闭加载状态
    if (allDataLoaded && materialDataLoading && materialDataInitializedRef.current) {
      perfLog.debug('所有材料数据已加载完成，关闭加载状态');
      setMaterialDataLoading(false);
    }
  }, [
    state.packagingConfig.materialAnalysis,
    materialData.papers.length,
    materialData.specialPapers.length,
    materialData.greyBoards.length,
    materialData.boxMaterials.length,
    materialData.printingMachines.length,
    materialData.materialSizes,
    materialDataLoading
  ]);

  // 验证材料配置数据结构
  const validateMaterialConfigData = useCallback((configs: any): boolean => {
    if (!configs || typeof configs !== 'object') {
      perfLog.warn('材料配置数据类型无效:', typeof configs);
      return false;
    }

    // 检查每个配置项的基本结构
    for (const [key, config] of Object.entries(configs)) {
      if (!config || typeof config !== 'object') {
        perfLog.warn(`材料配置项 ${key} 数据结构无效:`, config);
        return false;
      }
    }

    return true;
  }, []);

  // 监听材料配置变化，标记需要重新计算（避免API更新误触发）
  useEffect(() => {
    if (state.materialConfig?.partMaterialConfigs && Object.keys(state.materialConfig.partMaterialConfigs).length > 0) {
      // 验证数据结构
      if (!validateMaterialConfigData(state.materialConfig.partMaterialConfigs)) {
        perfLog.error('材料配置数据结构验证失败');
        setMaterialDataError('材料配置数据格式错误，请重新配置');
        return;
      }

      try {
        // 生成当前配置的唯一标识（只包含关键字段，忽略API更新的详细信息）
        const currentConfigKey = generateMaterialConfigKey(state.materialConfig.partMaterialConfigs);

        // 只有当关键配置真正发生变化时才触发
        if (lastMaterialConfigRef.current && lastMaterialConfigRef.current !== currentConfigKey) {
          if (!materialConfigChanged && state.packagingConfig.hasCalculatedImposition) {
            perfLog.debug('检测到真实的材料配置变化，标记需要重新计算', {
              oldConfig: lastMaterialConfigRef.current,
              newConfig: currentConfigKey
            });
            setMaterialConfigChanged(true);
            // 只清除拼版计算结果，不影响其他状态
            onUpdate.packagingConfig({
              hasCalculatedImposition: false,
              faceImpositionResults: [],
              greyImpositionResults: []
            });
          }
        }

        // 更新引用
        lastMaterialConfigRef.current = currentConfigKey;
      } catch (error) {
        perfLog.error('处理材料配置变化时出错:', error);
        setMaterialDataError('材料配置处理失败，请重试');
      }
    }
  }, [state.materialConfig?.partMaterialConfigs, validateMaterialConfigData, generateMaterialConfigKey, materialConfigChanged, state.packagingConfig.hasCalculatedImposition, onUpdate.packagingConfig]);

  // 创建默认部件组（基于原始部件信息）
  const createDefaultPartGroups = useCallback(() => {
    if (!state.packagingConfig.materialAnalysis) {
      perfLog.debug('没有材料分析结果，跳过部件组创建');
      return { facePartGroups: [], greyPartGroups: [] };
    }

    if (!state.basicInfo.parts || state.basicInfo.parts.length === 0) {
      perfLog.debug('没有部件数据，跳过部件组创建');
      return { facePartGroups: [], greyPartGroups: [] };
    }

    perfLog.debug('开始创建默认部件组', {
      partsCount: state.basicInfo.parts.length,
      canSelectFacePaper: state.packagingConfig.materialAnalysis.canSelectFacePaper,
      canSelectGreyBoard: state.packagingConfig.materialAnalysis.canSelectGreyBoard
    });

    // 计算部件尺寸信息
    const partDimensions = calculatePartDimensions();

    perfLog.debug('计算得到的部件尺寸:', partDimensions.length);

    // 创建默认部件组（每个部件一个组）- 修复：使用materialType字段直接过滤，避免重复显示
    const faceParts = state.packagingConfig.materialAnalysis.canSelectFacePaper
      ? partDimensions.filter(part => part.materialType === 'face')
      : [];
    const greyParts = state.packagingConfig.materialAnalysis.canSelectGreyBoard
      ? partDimensions.filter(part => part.materialType === 'grey')
      : [];

    perfLog.debug('部件材料类型过滤结果:', {
      totalPartDimensions: partDimensions.length,
      canSelectFacePaper: state.packagingConfig.materialAnalysis.canSelectFacePaper,
      canSelectGreyBoard: state.packagingConfig.materialAnalysis.canSelectGreyBoard,
      facePartsCount: faceParts.length,
      greyPartsCount: greyParts.length,
      facePartsDetails: faceParts.map(p => ({ name: p.part.name, formulas: p.part.formulas?.map(f => f.name) })),
      greyPartsDetails: greyParts.map(p => ({ name: p.part.name, formulas: p.part.formulas?.map(f => f.name) }))
    });

    const facePartGroups = faceParts.length > 0 ? createUnmergedPartGroups(faceParts, 'face') : [];
    const greyPartGroups = greyParts.length > 0 ? createUnmergedPartGroups(greyParts, 'grey') : [];

    perfLog.debug('创建的默认部件组:', {
      facePartGroupsCount: facePartGroups.length,
      greyPartGroupsCount: greyPartGroups.length
    });

    return { facePartGroups, greyPartGroups };
  }, [state.packagingConfig.materialAnalysis, state.basicInfo.parts, state.basicInfo.attributes]);

  // 根据合并设置生成最终部件组
  const generateFinalPartGroups = useCallback((mergeEnabled: boolean, mergeGroups: PartMergeGroup[]) => {
    // 首先获取默认部件组
    const { facePartGroups: defaultFaceGroups, greyPartGroups: defaultGreyGroups } = createDefaultPartGroups();

    if (!mergeEnabled || mergeGroups.length === 0) {
      // 不合并时，直接返回默认部件组
      perfLog.debug('不启用合并，返回默认部件组');
      return { facePartGroups: defaultFaceGroups, greyPartGroups: defaultGreyGroups };
    }

    perfLog.debug('开始应用部件合并', {
      mergeGroupsCount: mergeGroups.length,
      defaultFaceGroupsCount: defaultFaceGroups.length,
      defaultGreyGroupsCount: defaultGreyGroups.length
    });

    // 计算部件尺寸信息（用于合并计算）
    const partDimensions = calculatePartDimensions();

    let finalFaceGroups = [...defaultFaceGroups];
    let finalGreyGroups = [...defaultGreyGroups];

    // 应用用户的合并选择
    if (mergeEnabled && mergeGroups.length > 0) {
      // 处理面纸合并
      const faceGroup = mergeGroups.find(g => g.id === 'face_paper_group' && g.enabled);
      if (faceGroup && faceGroup.partIds.length > 1) {
        // 找到用户选择要合并的面纸部件 - 修复：使用materialType字段直接过滤
        const selectedFaceParts = partDimensions.filter(part =>
          faceGroup.partIds.includes(part.part.id!) && part.materialType === 'face'
        );

        if (selectedFaceParts.length > 1) {
          // 计算合并组的总尺寸 - 修正：使用加法而不是最大值
          // 横向排列：宽度相加，长度取最大值
          const totalWidth = selectedFaceParts.reduce((sum, p) => sum + p.width, 0);
          const totalLength = Math.max(...selectedFaceParts.map(p => p.length));
          const totalArea = selectedFaceParts.reduce((sum, p) => sum + p.area, 0);

          // 创建合并的部件组
          const mergedFaceGroup: PartGroup = {
            id: 'face_merged_group', // 修复：使用一致的命名规则
            name: `合并面纸组 (${selectedFaceParts.map(p => p.part.name).join('、')})`,
            parts: selectedFaceParts,
            totalLength,
            totalWidth,
            totalArea,
            materialType: 'face' as const
          };

          // 保留未被合并的面纸部件组 - 修复：使用materialType字段直接过滤
          const unmergedFaceParts = partDimensions.filter(part =>
            !faceGroup.partIds.includes(part.part.id!) && part.materialType === 'face'
          );
          const unmergedFaceGroups: PartGroup[] = unmergedFaceParts.map((part, index) => ({
            id: `face_${part.part.id!.toString()}_${index}`, // 修复：添加索引确保唯一性
            name: part.part.name,
            parts: [part],
            totalLength: part.length,
            totalWidth: part.width,
            totalArea: part.area,
            materialType: 'face' as const
          }));

          finalFaceGroups = [mergedFaceGroup, ...unmergedFaceGroups];
        }
      }

      // 处理灰板纸合并
      const greyGroup = mergeGroups.find(g => g.id === 'grey_board_group' && g.enabled);
      if (greyGroup && greyGroup.partIds.length > 1) {
        // 找到用户选择要合并的灰板纸部件 - 修复：使用materialType字段直接过滤
        const selectedGreyParts = partDimensions.filter(part =>
          greyGroup.partIds.includes(part.part.id!) && part.materialType === 'grey'
        );

        if (selectedGreyParts.length > 1) {
          // 计算合并组的总尺寸 - 修正：使用加法而不是最大值
          // 横向排列：宽度相加，长度取最大值
          const totalWidth = selectedGreyParts.reduce((sum, p) => sum + p.width, 0);
          const totalLength = Math.max(...selectedGreyParts.map(p => p.length));
          const totalArea = selectedGreyParts.reduce((sum, p) => sum + p.area, 0);

          // 创建合并的部件组
          const mergedGreyGroup: PartGroup = {
            id: 'grey_merged_group', // 修复：使用一致的命名规则
            name: `合并灰板纸组 (${selectedGreyParts.map(p => p.part.name).join('、')})`,
            parts: selectedGreyParts,
            totalLength,
            totalWidth,
            totalArea,
            materialType: 'grey' as const
          };

          // 保留未被合并的灰板纸部件组 - 修复：使用materialType字段直接过滤
          const unmergedGreyParts = partDimensions.filter(part =>
            !greyGroup.partIds.includes(part.part.id!) && part.materialType === 'grey'
          );
          const unmergedGreyGroups: PartGroup[] = unmergedGreyParts.map((part, index) => ({
            id: `grey_${part.part.id!.toString()}_${index}`, // 修复：添加索引确保唯一性
            name: part.part.name,
            parts: [part],
            totalLength: part.length,
            totalWidth: part.width,
            totalArea: part.area,
            materialType: 'grey' as const
          }));

          finalGreyGroups = [mergedGreyGroup, ...unmergedGreyGroups];
        }
      }
    }

    perfLog.debug('生成的最终部件组:', {
      finalFaceGroupsCount: finalFaceGroups.length,
      finalGreyGroupsCount: finalGreyGroups.length
    });

    return { facePartGroups: finalFaceGroups, greyPartGroups: finalGreyGroups };
  }, [createDefaultPartGroups, calculatePartDimensions, isPartForMaterial, createUnmergedPartGroups]);

  // 更新部件组到状态
  const updatePartGroupsToState = useCallback((facePartGroups: PartGroup[], greyPartGroups: PartGroup[]) => {
    // 检查是否真的需要更新状态（避免不必要的重新渲染）
    const currentFaceGroups = state.packagingConfig.facePartGroups || [];
    const currentGreyGroups = state.packagingConfig.greyPartGroups || [];

    const faceGroupsChanged = comparePartGroups(currentFaceGroups, facePartGroups);
    const greyGroupsChanged = comparePartGroups(currentGreyGroups, greyPartGroups);

    perfLog.debug('部件组变化检查:', {
      currentFaceGroupsCount: currentFaceGroups.length,
      currentGreyGroupsCount: currentGreyGroups.length,
      newFaceGroupsCount: facePartGroups.length,
      newGreyGroupsCount: greyPartGroups.length,
      faceGroupsChanged,
      greyGroupsChanged
    });

    if (faceGroupsChanged || greyGroupsChanged) {
      perfLog.debug('部件组发生变化，更新状态');

      // 更新状态，但保留其他重要信息
      onUpdate.packagingConfig({
        facePartGroups,
        greyPartGroups,
        // 如果有拼版计算结果，需要清除因为部件组变化了
        ...(faceGroupsChanged || greyGroupsChanged ? {
          hasCalculatedImposition: false,
          faceImpositionResults: [],
          greyImpositionResults: []
        } : {})
      });
    } else {
      perfLog.debug('部件组未发生变化，跳过状态更新');
    }
  }, [comparePartGroups, onUpdate.packagingConfig]);

  // 处理部件合并启用状态变化
  const handlePartMergingEnableChange = useCallback((enabled: boolean) => {
    const newMergeGroups = enabled ? state.packagingConfig.selectedMergeGroups || [] : [];

    onUpdate.packagingConfig({
      enablePartMerging: enabled,
      selectedMergeGroups: newMergeGroups
    });

    // 生成并更新部件组
    const { facePartGroups, greyPartGroups } = generateFinalPartGroups(enabled, newMergeGroups);
    updatePartGroupsToState(facePartGroups, greyPartGroups);
  }, [state.packagingConfig.selectedMergeGroups, onUpdate.packagingConfig, generateFinalPartGroups, updatePartGroupsToState]);

  // 处理合并组变化
  const handleMergeGroupsChange = useCallback((groups: PartMergeGroup[]) => {
    onUpdate.packagingConfig({
      selectedMergeGroups: groups
    });

    // 生成并更新部件组
    const { facePartGroups, greyPartGroups } = generateFinalPartGroups(state.packagingConfig.enablePartMerging || false, groups);
    updatePartGroupsToState(facePartGroups, greyPartGroups);
  }, [state.packagingConfig.enablePartMerging, onUpdate.packagingConfig, generateFinalPartGroups, updatePartGroupsToState]);

  // 初始化部件组 - 在材料分析完成后立即初始化
  useEffect(() => {
    // 只有在有材料分析结果，但没有部件组时才初始化
    const needsInitialization = (
      state.packagingConfig.materialAnalysis &&
      state.basicInfo.parts &&
      state.basicInfo.parts.length > 0 &&
      state.basicInfo.attributes &&
      !partGroupsInitializedRef.current &&
      (
        (state.packagingConfig.materialAnalysis.canSelectFacePaper && (!state.packagingConfig.facePartGroups || state.packagingConfig.facePartGroups.length === 0)) ||
        (state.packagingConfig.materialAnalysis.canSelectGreyBoard && (!state.packagingConfig.greyPartGroups || state.packagingConfig.greyPartGroups.length === 0))
      )
    );

    perfLog.debug('部件组初始化检查:', {
      hasMaterialAnalysis: !!state.packagingConfig.materialAnalysis,
      hasParts: !!state.basicInfo.parts,
      partsCount: state.basicInfo.parts?.length || 0,
      hasAttributes: !!state.basicInfo.attributes,
      alreadyInitialized: partGroupsInitializedRef.current,
      canSelectFacePaper: state.packagingConfig.materialAnalysis?.canSelectFacePaper,
      canSelectGreyBoard: state.packagingConfig.materialAnalysis?.canSelectGreyBoard,
      facePartGroupsCount: state.packagingConfig.facePartGroups?.length || 0,
      greyPartGroupsCount: state.packagingConfig.greyPartGroups?.length || 0,
      needsInitialization
    });

    if (needsInitialization) {
      perfLog.debug('开始初始化部件组');

      // 直接在这里生成部件组，避免循环依赖
      const { facePartGroups: defaultFaceGroups, greyPartGroups: defaultGreyGroups } = createDefaultPartGroups();

      // 应用合并设置（如果有的话）
      const mergeEnabled = state.packagingConfig.enablePartMerging || false;
      const mergeGroups = state.packagingConfig.selectedMergeGroups || [];

      let finalFaceGroups = defaultFaceGroups;
      let finalGreyGroups = defaultGreyGroups;

      // 如果启用了合并，应用合并逻辑
      if (mergeEnabled && mergeGroups.length > 0) {
        const result = generateFinalPartGroups(mergeEnabled, mergeGroups);
        finalFaceGroups = result.facePartGroups;
        finalGreyGroups = result.greyPartGroups;
      }

      // 直接更新状态，避免通过updatePartGroupsToState造成的循环依赖
      onUpdate.packagingConfig({
        facePartGroups: finalFaceGroups,
        greyPartGroups: finalGreyGroups
      });

      // 标记已初始化
      partGroupsInitializedRef.current = true;

      perfLog.debug('部件组初始化完成:', {
        facePartGroupsCount: finalFaceGroups.length,
        greyPartGroupsCount: finalGreyGroups.length
      });
    }
  }, [
    // 只依赖必要的状态，避免循环依赖
    state.packagingConfig.materialAnalysis,
    state.basicInfo.parts,
    state.basicInfo.attributes,
    state.packagingConfig.facePartGroups,
    state.packagingConfig.greyPartGroups,
    state.packagingConfig.enablePartMerging,
    state.packagingConfig.selectedMergeGroups
  ]);

  return (
    <div className="packaging-step">
      {/* 第一个块：选择部件合并 */}
      {state.basicInfo.parts && state.basicInfo.parts.length > 0 && (
        <PartMergeControl
          parts={state.basicInfo.parts}
          enablePartMerging={state.packagingConfig.enablePartMerging}
          selectedMergeGroups={state.packagingConfig.selectedMergeGroups || []}
          onEnableChange={handlePartMergingEnableChange}
          onMergeGroupsChange={handleMergeGroupsChange}
        />
      )}

      {/* 第二个块：选择部件材料 */}
      {useMemo(() => {
        // 显示材料数据加载错误
        if (materialDataError) {
          return (
            <Card title="材料数据" size="small" style={{ marginBottom: 16 }}>
              <Alert
                message="材料数据加载失败"
                description={materialDataError}
                type="error"
                showIcon
                action={
                  <Button
                    size="small"
                    onClick={() => {
                      setMaterialDataError(null);
                      materialDataInitializedRef.current = false;
                      if (state.packagingConfig.materialAnalysis) {
                        initializeMaterialData(state.packagingConfig.materialAnalysis);
                      }
                    }}
                  >
                    重试
                  </Button>
                }
              />
            </Card>
          );
        }

        if (!state.packagingConfig.materialAnalysis) {
          return (
            <Card title="材料分析" size="small" style={{ marginBottom: 16 }}>
              <Alert
                message="等待材料分析"
                description="请先完成第一步的基础信息填写。"
                type="warning"
                showIcon
              />
            </Card>
          );
        }

        // 显示材料数据加载状态
        if (materialDataLoading) {
          return (
            <Card title="材料数据" size="small" style={{ marginBottom: 16 }}>
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>正在加载材料数据...</div>
              </div>
            </Card>
          );
        }

        // 检查是否有足够的材料数据，如果没有且未在加载中，则显示加载状态
        const hasMinimalData = (
          (state.packagingConfig.materialAnalysis.canSelectFacePaper &&
           (materialData.papers.length > 0 || materialData.specialPapers.length > 0)) ||
          (state.packagingConfig.materialAnalysis.canSelectGreyBoard &&
           (materialData.greyBoards.length > 0 || materialData.boxMaterials.length > 0))
        );

        if (!hasMinimalData && !materialDataLoading && materialDataInitializedRef.current) {
          perfLog.debug('材料数据不足，显示加载状态:', {
            canSelectFacePaper: state.packagingConfig.materialAnalysis.canSelectFacePaper,
            canSelectGreyBoard: state.packagingConfig.materialAnalysis.canSelectGreyBoard,
            papersCount: materialData.papers.length,
            specialPapersCount: materialData.specialPapers.length,
            greyBoardsCount: materialData.greyBoards.length,
            boxMaterialsCount: materialData.boxMaterials.length
          });

          return (
            <Card title="材料数据" size="small" style={{ marginBottom: 16 }}>
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>等待材料数据加载...</div>
              </div>
            </Card>
          );
        }

        // 添加材料数据调试信息
        perfLog.debug('PackagingStep 传递给 MaterialSelectionControl 的材料数据:', {
          papersCount: materialData.papers.length,
          specialPapersCount: materialData.specialPapers.length,
          greyBoardsCount: materialData.greyBoards.length,
          boxMaterialsCount: materialData.boxMaterials.length,
          printingMachinesCount: materialData.printingMachines.length,
          hasMaterialSizes: !!materialData.materialSizes,
          dataFetched: materialData.dataFetched,
          materialDataInitialized: materialDataInitializedRef.current
        });

        return (
          <MaterialSelectionControl
            materialAnalysis={state.packagingConfig.materialAnalysis}
            facePartGroups={state.packagingConfig.facePartGroups || []}
            greyPartGroups={state.packagingConfig.greyPartGroups || []}
            selectedMaterials={state.materialConfig}
            onMaterialChange={onUpdate.materialConfig}
            onPartMaterialConfigChange={handlePartMaterialConfigChange}
            showValidation={state.packagingConfig.hasCalculatedImposition || false}
            initialPartMaterialConfigs={state.materialConfig.partMaterialConfigs || {}}
            // 新增：传递集中管理的材料数据，避免重复API调用
            materialData={materialData}
            allowDataInitialization={false} // 禁用组件内部的API调用
          />
        );
      }, [
        // 优化依赖项，减少不必要的重新渲染
        state.packagingConfig.materialAnalysis,
        state.packagingConfig.facePartGroups,
        state.packagingConfig.greyPartGroups,
        state.materialConfig,
        state.packagingConfig.hasCalculatedImposition,
        materialDataError,
        materialDataLoading,
        materialData,
        handlePartMaterialConfigChange,
        onUpdate.materialConfig
      ])}

      {/* 材料配置变化提示 */}
      {materialConfigChanged && (
        <Alert
          message="材料配置已变更"
          description="检测到材料配置发生变化，请重新点击「计算拼版」按钮进行计算。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 第三个块：拼版参数 */}
      <Card
        title={
          <Space>
            <CalculatorOutlined />
            <span>拼版参数</span>
          </Space>
        }
        size='small'
        style={{ marginBottom: 16 }}
        extra={
          <Button
            type="primary"
            size='small'
            icon={<CalculatorOutlined />}
            onClick={performPackagingCalculation}
            loading={calculating}
            disabled={!isMaterialConfigComplete}
            title={!isMaterialConfigComplete ? '请先完成所有部件组的材料配置' : ''}
          >
            计算拼版
          </Button>
        }
      >
        <Form layout="vertical">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Form.Item label="拼版设置">
                <Checkbox
                  checked={state.packagingConfig.enableOptimization}
                  onChange={(e) => onUpdate.packagingConfig({ enableOptimization: e.target.checked })}
                >
                  智能优化
                </Checkbox>
                <Checkbox
                  checked={state.packagingConfig.enableMaxImposition}
                  onChange={(e) => onUpdate.packagingConfig({ enableMaxImposition: e.target.checked })}
                >
                  最大拼版
                </Checkbox>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label={
                  <span>
                    排列方式
                    <Tooltip title="不交叉：产品整齐排列；交叉：相邻产品可旋转交错排列，提高材料利用率但增加工艺复杂度">
                      <InfoCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
                    </Tooltip>
                  </span>
                }
              >
                <Select
                  value={state.packagingConfig.arrangementMode || 'normal'}
                  onChange={(value) => onUpdate.packagingConfig({ arrangementMode: value })}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="normal">不交叉</Select.Option>
                  <Select.Option value="cross">交叉</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="长度留边 (mm)">
                <InputNumber
                  value={state.packagingConfig.marginLength}
                  onChange={value => onUpdate.packagingConfig({ marginLength: value || DEFAULT_VALUES.MARGIN_LENGTH })}
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="宽度留边 (mm)">
                <InputNumber
                  value={state.packagingConfig.marginWidth}
                  onChange={value => onUpdate.packagingConfig({ marginWidth: value || DEFAULT_VALUES.MARGIN_WIDTH })}
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 展示拼板结果 */}
      {hasImpositionResults && (
          <ImpositionLogicDisplay
            impositionResults={[
              ...(state.packagingConfig.faceImpositionResults || []),
              ...(state.packagingConfig.greyImpositionResults || [])
            ]}
            showDetails={true}
            facePartGroups={state.packagingConfig.facePartGroups || []}
            greyPartGroups={state.packagingConfig.greyPartGroups || []}
            bleed={state.packagingConfig.bleed || 3}
            gripper={state.packagingConfig.gripper || 10}
            bite={state.packagingConfig.bite || 15}
            partMaterialConfigs={state.materialConfig?.partMaterialConfigs}
            boxQuantity={state.basicInfo.quantity || 1}
            onMaterialCostChange={(cost, details) => {
              // 将材料费用和详细信息存储到packagingConfig中
              const updatedConfig = {
                materialCost: cost,
                materialCostDetails: details || []
              };
              onUpdate.packagingConfig(updatedConfig);
              perfLog.debug('材料费用已更新到packagingConfig:', { cost, detailsCount: details?.length || 0 });
            }}
          />
        )}
      {!state.packagingConfig.materialAnalysis && (
        <Alert
          message="等待计算"
          description="请先完成第一步的基础信息填写，然后进行拼版计算。"
          type="info"
          showIcon
        />
      )}
    </div>
  );
};

export default PackagingStep;
