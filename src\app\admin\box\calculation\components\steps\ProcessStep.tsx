'use client';

import React, { useState } from 'react';
import {
  Card, Row, Col, Button, Table, Space,
  Modal, Typography, Alert, message, Tag, InputNumber, Form} from 'antd';
import {
  PlusOutlined, DeleteOutlined, SettingOutlined} from '@ant-design/icons';
import { CalculationState, ProcessConfig, ProcessItem, PartMaterialConfig } from '../../types/calculation';
import { useAsyncError } from '@/lib/hooks/useErrorHandler';
import {
  printingApi, surfaceProcessApi, silkScreenProcessApi, hotStampingProcessApi,
  laminatingProcessApi, corrugatedProcessApi, embossingProcess<PERSON>pi,
  texturingProcess<PERSON>pi, hydraulicProcess<PERSON>pi,
  dieCuttingProcessApi, dieCuttingPlateFeeApi
} from '@/services/adminApi';
import {
  Printing, SurfaceProcess, SilkScreenProcess, HotStampingProcess,
  LaminatingProcess, CorrugatedProcess, EmbossingProcess,
  TexturingProcess, HydraulicProcess,
  DieCuttingProcess, DieCuttingPlateFee
} from '@/types/craftSalary';
import { PartGroup } from '../../types/packaging';

import { Box } from '@/types/box';
import MaterialInfoDisplay from '../MaterialInfoDisplay';
import { perfLog } from '@/lib/utils/perfLog';
import {
  calculateBasePriceWithMinimum,
  calculateTieredPrice,
  calculateAreaBasedPrice,
  calculateCompositePrice
} from '../../util';

const { Title, Text } = Typography;

interface ProcessStepProps {
  state: CalculationState;
  onUpdate: {
    processConfig: (data: Partial<ProcessConfig>) => void;
  };
  onRecalculate: () => void;
  sourceBox?: Box | null;
}

/**
 * 工艺选择步骤组件
 */
const ProcessStep: React.FC<ProcessStepProps> = ({
  state,
  onUpdate,
  sourceBox
}) => {
  const [processModalVisible, setProcessModalVisible] = useState(false);
  const [selectedProcessType, setSelectedProcessType] = useState<string>('');

  // 凹凸工艺子类型选择模态框
  const [embossingSubTypeModalVisible, setEmbossingSubTypeModalVisible] = useState(false);
  const [selectedEmbossingSubType, setSelectedEmbossingSubType] = useState<'texturing' | 'embossing' | 'hydraulic' | ''>('');

  // 烫金工艺尺寸参数模态框
  const [hotStampingDimensionsModalVisible, setHotStampingDimensionsModalVisible] = useState(false);
  const [selectedHotStampingProcess, setSelectedHotStampingProcess] = useState<any>(null);
  const [hotStampingLength, setHotStampingLength] = useState<number>(0);
  const [hotStampingWidth, setHotStampingWidth] = useState<number>(0);

  // 凹凸工艺尺寸参数模态框
  const [embossingDimensionsModalVisible, setEmbossingDimensionsModalVisible] = useState(false);
  const [selectedEmbossingProcess, setSelectedEmbossingProcess] = useState<any>(null);
  const [embossingLength, setEmbossingLength] = useState<number>(0);
  const [embossingWidth, setEmbossingWidth] = useState<number>(0);

  // 液压工艺尺寸参数模态框
  const [hydraulicDimensionsModalVisible, setHydraulicDimensionsModalVisible] = useState(false);
  const [selectedHydraulicProcess, setSelectedHydraulicProcess] = useState<any>(null);
  const [hydraulicLength, setHydraulicLength] = useState<number>(0);
  const [hydraulicWidth, setHydraulicWidth] = useState<number>(0);

  // 工艺数据状态
  const [printingList, setPrintingList] = useState<Printing[]>([]);
  const [surfaceProcessList, setSurfaceProcessList] = useState<SurfaceProcess[]>([]);
  const [silkScreenList, setSilkScreenList] = useState<SilkScreenProcess[]>([]);
  const [hotStampingList, setHotStampingList] = useState<HotStampingProcess[]>([]);
  const [laminatingList, setLaminatingList] = useState<LaminatingProcess[]>([]);
  const [corrugatedList, setCorrugatedList] = useState<CorrugatedProcess[]>([]);
  const [embossingList, setEmbossingList] = useState<EmbossingProcess[]>([]);
  const [texturingList, setTexturingList] = useState<TexturingProcess[]>([]);
  const [hydraulicList, setHydraulicList] = useState<HydraulicProcess[]>([]);
  const [dieCuttingList, setDieCuttingList] = useState<DieCuttingProcess[]>([]);
  const [dieCuttingPlateFeeList, setDieCuttingPlateFeeList] = useState<DieCuttingPlateFee[]>([]);



  const { execute, loading } = useAsyncError();

  // ==================== 通用价格计算工具函数 ====================

  // 使用工具函数进行基础价格计算

  // 使用工具函数进行阶梯价格计算

  // 使用工具函数进行面积价格计算

  // 使用工具函数进行复合价格计算

  // ==================== 具体工艺价格计算函数 ====================

  // 计算印刷价格的函数
  const calculatePrintingPrice = (printingData: any, quantity: number): { unitPrice: number; totalPrice: number } => {
    perfLog.debug('计算印刷价格:', { printingData, quantity });

    // 定义印刷价格阶梯
    const printingTiers = {
      tier1: { min: 1000, max: 1999, field: 'price1000_1999' },
      tier2: { min: 2000, max: 2999, field: 'price2000_2999' },
      tier3: { min: 3000, max: 3999, field: 'price3000_3999' },
      tier4: { min: 4000, max: 4999, field: 'price4000_4999' },
      tier5: { min: 5000, max: 5999, field: 'price5000_5999' },
      tier6: { min: 6000, max: 6999, field: 'price6000_6999' },
      tier7: { min: 7000, max: 7999, field: 'price7000_7999' },
      tier8: { min: 8000, max: 8999, field: 'price8000_8999' },
      tier9: { min: 9000, max: 9999, field: 'price9000_9999' },
      tier10: { min: 10000, max: Infinity, field: 'price10000Plus' }
    };

    const { unitPrice, priceSource } = calculateTieredPrice(printingData, quantity, printingTiers);

    // 获取CTP版费和专色费
    const ctpPlateFee = Number(printingData.ctpPlateFee) || 0;
    const spotColorFee = Number(printingData.spotColorFee) || 0;
    const additionalFees = ctpPlateFee + spotColorFee;

    // 计算基础印刷费用（不含版费）
    const basePrintingCost = unitPrice * quantity;
    const basePrice = Number(printingData.basePrice) || 0;

    // 应用起步价逻辑
    const finalPrintingCost = basePrice > 0 && basePrintingCost < basePrice ? basePrice : basePrintingCost;
    const finalTotalPrice = finalPrintingCost + additionalFees;
    const finalUnitPrice = Number((finalTotalPrice / quantity).toFixed(2));

    perfLog.debug('印刷价格计算结果:', {
      unitPrice: finalUnitPrice,
      totalPrice: finalTotalPrice,
      quantity,
      priceSource,
      basePrice,
      basePrintingCost,
      finalPrintingCost,
      additionalFees,
      usedBasePrice: finalPrintingCost === basePrice,
      ctpPlateFee,
      spotColorFee
    });

    return { unitPrice: finalUnitPrice, totalPrice: finalTotalPrice };
  };

  // 计算其他工艺价格的函数（应用起步价逻辑）
  const calculateProcessPrice = (processData: any, quantity: number = 1): { unitPrice: number; totalPrice: number } => {
    perfLog.debug('计算工艺价格:', { processData, quantity });

    // 获取单价（不同工艺类型字段名可能不同）
    const unitPrice = processData.price || processData.unitPrice || 0;
    const basePrice = Number(processData.basePrice) || 0;

    const result = calculateBasePriceWithMinimum(unitPrice, quantity, basePrice);

    perfLog.debug('工艺价格计算结果:', {
      unitPrice: result.unitPrice,
      totalPrice: result.totalPrice,
      quantity,
      basePrice,
      usedBasePrice: result.usedBasePrice
    });

    return { unitPrice: result.unitPrice, totalPrice: result.totalPrice };
  };

  // 计算压纹工艺价格的函数（阶梯计价）
  const calculateTexturingPrice = (texturingData: any, quantity: number): { unitPrice: number; totalPrice: number } => {
    perfLog.debug('计算压纹工艺价格:', { texturingData, quantity });

    // 定义压纹价格阶梯
    const texturingTiers = {
      tier1: { min: 0, max: 999, field: 'priceBelow1000' },
      tier2: { min: 1000, max: 1999, field: 'price1000_1999' },
      tier3: { min: 2000, max: 3999, field: 'price2000_3999' },
      tier4: { min: 4000, max: Infinity, field: 'price4000Plus' }
    };

    let totalPrice = 0;
    let priceSource = 'default';

    // 根据数量选择对应的价格阶梯
    for (const [, config] of Object.entries(texturingTiers)) {
      if (quantity >= config.min && quantity <= config.max) {
        totalPrice = Number(texturingData[config.field]) || 0;
        priceSource = config.field;
        break;
      }
    }

    // 压纹版费
    const textureVersionFee = Number(texturingData.textureVersion) || 0;
    const finalTotalPrice = totalPrice + textureVersionFee;

    // 计算单价（用于界面显示）
    const unitPrice = quantity > 0 ? finalTotalPrice / quantity : 0;

    perfLog.debug('压纹工艺价格计算结果:', {
      unitPrice,
      totalPrice: finalTotalPrice,
      quantity,
      priceSource,
      textureVersionFee,
      baseTotalPrice: totalPrice
    });

    return { unitPrice, totalPrice: finalTotalPrice };
  };

  // 计算凹凸工艺和液压工艺价格的函数（材料费+工资费）
  const calculateEmbossingHydraulicPrice = (
    processData: any,
    quantity: number,
    unit: string,
    dimensions: { length: number; width: number }
  ): { unitPrice: number; totalPrice: number } => {
    perfLog.debug('计算凹凸/液压工艺价格:', { processData, quantity, unit, dimensions });

    // 根据单位确定实际计算数量
    let materialQuantity: number;
    let salaryQuantity: number;

    if (unit === '元/个') {
      // 按个数计费：使用盒子数量
      materialQuantity = quantity;
      salaryQuantity = quantity;
    } else if (unit === '元/平方') {
      // 按面积计费
      materialQuantity = calculateAreaBasedPrice(1, dimensions); // 面积（平方米）
      salaryQuantity = quantity; // 工资费按张数计算
    } else {
      // 其他单位按原数量计算
      materialQuantity = quantity;
      salaryQuantity = quantity;
    }

    // 使用复合价格计算函数
    const priceResult = calculateCompositePrice(processData, materialQuantity, salaryQuantity);

    // 单价计算：综合计费时按盒子数量计算单价（每个盒子的平均价格）
    const unitPrice = quantity > 0 ? priceResult.totalPrice / quantity : 0;

    perfLog.debug('凹凸/液压工艺价格计算结果:', {
      unitPrice,
      totalPrice: priceResult.totalPrice,
      quantity,
      unit,
      materialQuantity,
      salaryQuantity,
      materialCost: priceResult.materialCost,
      salaryCost: priceResult.salaryCost
    });

    return { unitPrice, totalPrice: priceResult.totalPrice };
  };

  // 计算刀版费价格的函数（特殊处理逻辑）
  const calculateDieCuttingPlateFeePrice = (
    processData: any,
    partGroupId: string
  ): { unitPrice: number; totalPrice: number; quantity: number; unit: string } => {
    perfLog.debug('计算刀版费价格:', { processData, partGroupId });

    const unit = processData.unit || '元/个';
    const price = Number(processData.price) || 0;
    const basePrice = Number(processData.basePrice) || 0;
    const impositionQuantity = Number(processData.impositionQuantity) || 0;

    let finalTotalPrice: number;
    let displayQuantity: number;
    let displayUnit: string;

    if (unit === '元/个') {
      // 按个数计费：直接使用价格，不乘以数量
      finalTotalPrice = price;
      displayQuantity = 1;
      displayUnit = '元';

      perfLog.debug('刀版费按个数计费:', {
        price,
        finalTotalPrice,
        displayQuantity,
        displayUnit
      });
    } else {
      // 按面积或拼版数量计费
      const { quantity: sheetsQuantity, area } = getSurfaceProcessQuantityAndArea(partGroupId);
      const areaPriceTotal = price * area;

      if (impositionQuantity > 0 && sheetsQuantity > 0) {
        // 有拼版数量设置且有拼版结果
        const impositionPriceTotal = Math.max(sheetsQuantity * impositionQuantity, basePrice);
        finalTotalPrice = Math.max(areaPriceTotal, impositionPriceTotal);

        perfLog.debug('刀版费按拼版数量计费:', {
          areaPriceTotal,
          impositionPriceTotal,
          sheetsQuantity,
          impositionQuantity,
          basePrice,
          finalTotalPrice
        });
      } else {
        // 没有拼版数量设置，按面积计算并应用起步价
        finalTotalPrice = Math.max(areaPriceTotal, basePrice);

        perfLog.debug('刀版费按面积计费（无拼版）:', {
          area,
          price,
          areaPriceTotal,
          basePrice,
          finalTotalPrice
        });
      }

      displayQuantity = area;
      displayUnit = unit;
    }

    // 计算单价
    const unitPrice = displayQuantity > 0 ? finalTotalPrice / displayQuantity : finalTotalPrice;

    perfLog.debug('刀版费价格计算结果:', {
      unitPrice,
      totalPrice: finalTotalPrice,
      quantity: displayQuantity,
      unit: displayUnit
    });

    return {
      unitPrice,
      totalPrice: finalTotalPrice,
      quantity: displayQuantity,
      unit: displayUnit
    };
  };

  // ==================== 数量计算工具函数 ====================

  // 获取面纸张数的函数
  const getFacePaperQuantity = (): number => {
    perfLog.debug('获取面纸张数，当前状态:', {
      faceImpositionResults: state.packagingConfig.faceImpositionResults,
      materialConfig: state.materialConfig,
      basicInfo: state.basicInfo
    });

    // 优先从拼版结果中获取张数
    const faceImpositionResults = state.packagingConfig.faceImpositionResults || [];

    if (faceImpositionResults.length > 0) {
      // 从拼版结果中获取所需的总张数
      const totalSheets = faceImpositionResults.reduce((sum, result) => {
        return sum + (result.sheetsNeeded || 0);
      }, 0);

      perfLog.debug('从拼版结果获取的面纸张数:', totalSheets);
      return totalSheets;
    }

    // 如果没有拼版结果，从材料配置中估算
    const partMaterialConfigs = state.materialConfig.partMaterialConfigs || {};
    let totalSheets = 0;

    // 遍历所有部件材料配置，查找面纸相关的配置
    Object.values(partMaterialConfigs).forEach(config => {
      if (config.materialCategory === 'paper' || config.materialCategory === 'specialPaper') {
        // 简单估算：假设每个盒子需要1张面纸
        totalSheets += state.basicInfo.quantity || 1;
      }
    });

    perfLog.debug('从材料配置估算的面纸张数:', totalSheets);

    // 如果都没有，使用基础数量
    const fallbackQuantity = totalSheets || state.basicInfo.quantity || 1;
    perfLog.debug('最终使用的面纸张数:', fallbackQuantity);

    return fallbackQuantity;
  };

  // 获取覆膜工艺的计算数量和面积
  const getSurfaceProcessQuantityAndArea = (partGroupId: string): { quantity: number; area: number } => {
    perfLog.debug('开始获取覆膜工艺计算数量和面积，部件组ID:', partGroupId);

    // 从拼版结果中获取该部件组的信息
    const faceImpositionResults = state.packagingConfig.faceImpositionResults || [];
    const greyImpositionResults = state.packagingConfig.greyImpositionResults || [];
    const allImpositionResults = [...faceImpositionResults, ...greyImpositionResults];

    const partGroupResult = allImpositionResults.find(result => result.partGroup.id === partGroupId);

    if (partGroupResult) {
      const sheetsNeeded = partGroupResult.sheetsNeeded || 0;
      const materialWidth = partGroupResult.materialWidth || 0; // mm
      const materialLength = partGroupResult.materialLength || 0; // mm
      const materialArea = calculateAreaBasedPrice(1, { length: materialLength, width: materialWidth });

      perfLog.debug('从拼版结果获取覆膜数据:', {
        partGroupId,
        sheetsNeeded,
        materialWidth,
        materialLength,
        materialArea,
      });
      return { quantity: sheetsNeeded, area: materialArea };
    }

    // 如果没有拼版结果，使用基础数量估算
    const fallbackQuantity = state.basicInfo.quantity || 1;
    const fallbackArea = fallbackQuantity * 0.1; // 假设每个盒子0.1平方米覆膜面积

    perfLog.debug('使用估算覆膜数据:', { quantity: fallbackQuantity, area: fallbackArea });
    return { quantity: fallbackQuantity, area: fallbackArea };
  };

  // 获取工艺计算数量 - 通用函数，根据单位类型返回相应的数量
  const getProcessCalculationQuantity = (partGroupId: string, unit: string): number => {
    perfLog.debug('开始获取工艺计算数量，部件组ID:', partGroupId, '单位:', unit);

    const { quantity: sheetsQuantity, area } = getSurfaceProcessQuantityAndArea(partGroupId);
    const boxQuantity = state.basicInfo.quantity || 1;

    switch (unit) {
      case '元/张':
        return sheetsQuantity;
      case '元/平方':
        return area * sheetsQuantity;
      case '元/个':
        return boxQuantity;
      default:
        perfLog.warn('未知的单位类型:', unit, '使用默认数量1');
        return 1;
    }
  };

  // 获取部件组的默认尺寸（用于凹凸和液压工艺）
  const getPartGroupDefaultDimensions = (partGroupId: string): { length: number; width: number } => {
    perfLog.debug('获取部件组默认尺寸，部件组ID:', partGroupId);

    // 从拼版结果中获取该部件组的信息
    const faceImpositionResults = state.packagingConfig.faceImpositionResults || [];
    const greyImpositionResults = state.packagingConfig.greyImpositionResults || [];
    const allImpositionResults = [...faceImpositionResults, ...greyImpositionResults];

    const partGroupResult = allImpositionResults.find(result => result.partGroup.id === partGroupId);

    if (partGroupResult && partGroupResult.partGroup.parts.length > 0) {
      // 使用第一个部件的尺寸作为默认值
      const firstPart = partGroupResult.partGroup.parts[0];
      perfLog.debug('从拼版结果获取部件尺寸:', {
        partGroupId,
        length: firstPart.length,
        width: firstPart.width
      });
      return {
        length: firstPart.length || 100,
        width: firstPart.width || 100
      };
    }

    // 如果没有拼版结果，尝试从基础信息中获取
    const parts = state.basicInfo.parts || [];
    if (parts.length > 0) {
      // 使用默认尺寸值
      perfLog.debug('从基础信息获取部件尺寸（使用默认值）');
      return {
        length: 100, // 默认长度 100mm
        width: 100   // 默认宽度 100mm
      };
    }

    perfLog.debug('未找到部件尺寸信息，使用默认值');
    return {
      length: 100, // 默认长度 100mm
      width: 100   // 默认宽度 100mm
    };
  };

  // 获取工艺数据
  const fetchProcessData = async (processType: string) => {
    const result = await execute(async () => {
      const params = { page: 1, pageSize: 100 };

      switch (processType) {
        case 'printing':
          return await printingApi.getList(params);
        case 'surface':
          return await surfaceProcessApi.getList(params);
        case 'silkScreen':
          return await silkScreenProcessApi.getList(params);
        case 'hotStamping':
          return await hotStampingProcessApi.getList(params);
        case 'laminating':
          return await laminatingProcessApi.getList(params);
        case 'corrugated':
          return await corrugatedProcessApi.getList(params);
        case 'embossing':
          return await embossingProcessApi.getList(params);
        case 'texturing':
          return await texturingProcessApi.getList(params);
        case 'hydraulic':
          return await hydraulicProcessApi.getList(params);
        case 'dieCutting':
          return await dieCuttingProcessApi.getList(params);
        case 'dieCuttingPlateFee':
          return await dieCuttingPlateFeeApi.getList(params);
        default:
          throw new Error('未知的工艺类型')
      }
    }, `获取${getProcessTypeName(processType)}数据`);

    if (result) {
      switch (processType) {
        case 'printing':
          setPrintingList(result.list || []);
          break;
        case 'surface':
          setSurfaceProcessList(result.list || []);
          break;
        case 'silkScreen':
          setSilkScreenList(result.list || []);
          break;
        case 'hotStamping':
          setHotStampingList(result.list || []);
          break;
        case 'laminating':
          setLaminatingList(result.list || []);
          break;
        case 'corrugated':
          setCorrugatedList(result.list || []);
          break;
        case 'embossing':
          setEmbossingList(result.list || []);
          break;
        case 'texturing':
          setTexturingList(result.list || []);
          break;
        case 'hydraulic':
          setHydraulicList(result.list || []);
          break;
        case 'dieCutting':
          setDieCuttingList(result.list || []);
          break;
        case 'dieCuttingPlateFee':
          setDieCuttingPlateFeeList(result.list || []);
          break;
      }
    }
  };

  // 获取当前的部件分组信息
  const getCurrentPartGroups = (): PartGroup[] => {
    const facePartGroups = state.packagingConfig.facePartGroups || [];
    const greyPartGroups = state.packagingConfig.greyPartGroups || [];
    return [...facePartGroups, ...greyPartGroups];
  };

  // 获取部件分组对应的材料张数
  const getPartGroupSheetsNeeded = (partGroupId: string): number | undefined => {
    const faceImpositionResults = state.packagingConfig.faceImpositionResults || [];
    const greyImpositionResults = state.packagingConfig.greyImpositionResults || [];
    const allImpositionResults = [...faceImpositionResults, ...greyImpositionResults];

    // 查找对应部件组的拼版结果
    const impositionResult = allImpositionResults.find(result =>
      result.partGroup.id === partGroupId
    );

    return impositionResult?.sheetsNeeded;
  };

  // 获取工艺类型名称
  const getProcessTypeName = (type: string): string => {
    const typeNames = {
      printing: '印刷工艺',
      surface: '覆膜工艺',
      silkScreen: '丝网印刷',
      hotStamping: '烫金工艺',
      laminating: '对裱工艺',
      corrugated: '瓦楞工艺',
      embossing: '凹凸工艺',
      texturing: '压纹工艺',
      hydraulic: '液压工艺',
      dieCutting: '模切工艺',
      dieCuttingPlateFee: '刀版费'
    };
    return typeNames[type as keyof typeof typeNames] || type;
  };

  // 处理烫金工艺尺寸参数确认
  const handleHotStampingDimensionsConfirm = () => {
    if (!selectedHotStampingProcess || hotStampingLength <= 0 || hotStampingWidth <= 0) {
      message.error('请输入有效的烫金尺寸参数');
      return;
    }

    if (!selectedPartGroupId) {
      message.error('未选择部件组');
      return;
    }

    // 计算烫金工艺的价格
    const { quantity: sheetsQuantity } = getSurfaceProcessQuantityAndArea(selectedPartGroupId);

    // 计算烫金面积（用户输入的尺寸）
    const hotStampingArea = (hotStampingLength * hotStampingWidth) / 1000000; // 转换为平方米
    const totalHotStampingArea = hotStampingArea * sheetsQuantity; // 总烫金面积

    // 工资费用（按张数计算）
    const salaryCost = (selectedHotStampingProcess.salary || 0) * sheetsQuantity;

    // 材料费用（按烫金面积计算）
    const materialCost = (selectedHotStampingProcess.materialPrice || 0) * totalHotStampingArea;

    // 总费用
    const hotStampingTotalCost = salaryCost + materialCost;
    const unitPrice = sheetsQuantity > 0 ? hotStampingTotalCost / sheetsQuantity : 0;

    // 应用起步价逻辑
    const basePrice = selectedHotStampingProcess.basePrice || 0;
    const finalTotalCost = Math.max(hotStampingTotalCost, basePrice);
    const finalUnitPrice = sheetsQuantity > 0 ? finalTotalCost / sheetsQuantity : unitPrice;

    // 创建带有尺寸参数的烫金工艺项目
    const processItem: ProcessItem = {
      id: selectedHotStampingProcess.id,
      name: selectedHotStampingProcess.name,
      quantity: sheetsQuantity,
      unit: '综合计费',
      unitPrice: finalUnitPrice,
      totalPrice: finalTotalCost,
      parameters: selectedHotStampingProcess,
      hotStampingDimensions: {
        length: hotStampingLength,
        width: hotStampingWidth
      }
    };

    // 更新部件组工艺配置
    const updatedConfig = { ...state.processConfig };
    const partGroupConfigs = { ...updatedConfig.partGroupProcessConfigs };

    if (!partGroupConfigs[selectedPartGroupId]) {
      // 获取部件组信息
      const partGroup = getCurrentPartGroups().find(pg => pg.id === selectedPartGroupId);
      partGroupConfigs[selectedPartGroupId] = {
        partGroupId: selectedPartGroupId,
        partGroupName: partGroup?.name || '',
        materialType: partGroup?.materialType || 'face',
        groupProcessCost: 0
      };
    }

    const groupConfig = partGroupConfigs[selectedPartGroupId];
    groupConfig.hotStamping = [...(groupConfig.hotStamping || []), processItem];

    // 重新计算该部件组的工艺总成本
    const groupTotalCost = Object.values(groupConfig)
      .filter((items): items is ProcessItem[] => Array.isArray(items))
      .flat()
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0);

    groupConfig.groupProcessCost = groupTotalCost;

    // 重新计算总工艺成本
    const totalCost = Object.values(partGroupConfigs)
      .reduce((sum, config) => sum + (config.groupProcessCost || 0), 0);

    updatedConfig.partGroupProcessConfigs = partGroupConfigs;
    updatedConfig.processCost = totalCost;

    onUpdate.processConfig(updatedConfig);
    setHotStampingDimensionsModalVisible(false);
    setProcessModalVisible(false);
    message.success('已添加烫金工艺');

    // 重置状态
    setSelectedHotStampingProcess(null);
    setHotStampingLength(0);
    setHotStampingWidth(0);
  };

  // 处理凹凸工艺尺寸参数确认
  const handleEmbossingDimensionsConfirm = () => {
    if (!selectedEmbossingProcess || embossingLength <= 0 || embossingWidth <= 0) {
      message.error('请输入有效的凹凸工艺尺寸参数');
      return;
    }

    if (!selectedPartGroupId) {
      message.error('未选择部件组');
      return;
    }

    // 计算凹凸工艺的价格
    const unit = selectedEmbossingProcess.unit || '元/个';
    const boxQuantity = state.basicInfo.quantity || 1; // 使用盒子数量

    const priceResult = calculateEmbossingHydraulicPrice(
      selectedEmbossingProcess,
      boxQuantity, // 传入盒子数量
      unit,
      { length: embossingLength, width: embossingWidth }
    );

    // 创建带有尺寸参数的凹凸工艺项目
    const processItem: ProcessItem = {
      id: selectedEmbossingProcess.id,
      name: selectedEmbossingProcess.name,
      quantity: boxQuantity, // 显示盒子数量
      unit: '综合计费',
      unitPrice: priceResult.unitPrice,
      totalPrice: priceResult.totalPrice,
      parameters: selectedEmbossingProcess,
      embossingDimensions: {
        length: embossingLength,
        width: embossingWidth
      }
    };

    // 添加到配置中
    const updatedConfig = { ...state.processConfig };
    const partGroupConfigs = { ...updatedConfig.partGroupProcessConfigs };

    if (!partGroupConfigs[selectedPartGroupId]) {
      partGroupConfigs[selectedPartGroupId] = {
        partGroupId: selectedPartGroupId,
        partGroupName: '',
        materialType: 'face',
        groupProcessCost: 0
      };
    }

    const groupConfig = partGroupConfigs[selectedPartGroupId];
    groupConfig.embossing = [...(groupConfig.embossing || []), processItem];

    // 重新计算该部件组的工艺总成本
    const groupTotalCost = Object.values(groupConfig)
      .filter((items): items is ProcessItem[] => Array.isArray(items))
      .flat()
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0);

    groupConfig.groupProcessCost = groupTotalCost;

    // 重新计算总工艺成本
    const totalCost = Object.values(partGroupConfigs)
      .reduce((sum, config) => sum + (config.groupProcessCost || 0), 0);

    updatedConfig.partGroupProcessConfigs = partGroupConfigs;
    updatedConfig.processCost = totalCost;

    onUpdate.processConfig(updatedConfig);
    setEmbossingDimensionsModalVisible(false);
    setProcessModalVisible(false);
    message.success('已添加凹凸工艺');

    // 重置状态
    setSelectedEmbossingProcess(null);
    setEmbossingLength(0);
    setEmbossingWidth(0);
  };

  // 处理液压工艺尺寸参数确认
  const handleHydraulicDimensionsConfirm = () => {
    if (!selectedHydraulicProcess || hydraulicLength <= 0 || hydraulicWidth <= 0) {
      message.error('请输入有效的液压工艺尺寸参数');
      return;
    }

    if (!selectedPartGroupId) {
      message.error('未选择部件组');
      return;
    }

    // 计算液压工艺的价格
    const unit = selectedHydraulicProcess.unit || '元/个';
    const boxQuantity = state.basicInfo.quantity || 1; // 使用盒子数量

    const priceResult = calculateEmbossingHydraulicPrice(
      selectedHydraulicProcess,
      boxQuantity, // 传入盒子数量
      unit,
      { length: hydraulicLength, width: hydraulicWidth }
    );

    // 创建带有尺寸参数的液压工艺项目
    const processItem: ProcessItem = {
      id: selectedHydraulicProcess.id,
      name: selectedHydraulicProcess.name,
      quantity: boxQuantity, // 显示盒子数量
      unit: '综合计费',
      unitPrice: priceResult.unitPrice,
      totalPrice: priceResult.totalPrice,
      parameters: selectedHydraulicProcess,
      hydraulicDimensions: {
        length: hydraulicLength,
        width: hydraulicWidth
      }
    };

    // 添加到配置中
    const updatedConfig = { ...state.processConfig };
    const partGroupConfigs = { ...updatedConfig.partGroupProcessConfigs };

    if (!partGroupConfigs[selectedPartGroupId]) {
      partGroupConfigs[selectedPartGroupId] = {
        partGroupId: selectedPartGroupId,
        partGroupName: '',
        materialType: 'face',
        groupProcessCost: 0
      };
    }

    const groupConfig = partGroupConfigs[selectedPartGroupId];
    groupConfig.embossing = [...(groupConfig.embossing || []), processItem]; // 液压工艺也归类到embossing

    // 重新计算该部件组的工艺总成本
    const groupTotalCost = Object.values(groupConfig)
      .filter((items): items is ProcessItem[] => Array.isArray(items))
      .flat()
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0);

    groupConfig.groupProcessCost = groupTotalCost;

    // 重新计算总工艺成本
    const totalCost = Object.values(partGroupConfigs)
      .reduce((sum, config) => sum + (config.groupProcessCost || 0), 0);

    updatedConfig.partGroupProcessConfigs = partGroupConfigs;
    updatedConfig.processCost = totalCost;

    onUpdate.processConfig(updatedConfig);
    setHydraulicDimensionsModalVisible(false);
    setProcessModalVisible(false);
    message.success('已添加液压工艺');

    // 重置状态
    setSelectedHydraulicProcess(null);
    setHydraulicLength(0);
    setHydraulicWidth(0);
  };

  // 添加工艺项目 - 支持多选，按部件分组
  const handleAddProcess = (processData: any, processType: string) => {
    perfLog.debug('添加工艺项目:', { processData, processType, selectedPartGroupId });

    if (!selectedPartGroupId) {
      message.error('未选择部件组');
      return;
    }

    // 烫金工艺需要特殊处理，先输入尺寸参数
    if (processType === 'hotStamping') {
      setSelectedHotStampingProcess(processData);
      // 设置默认尺寸
      const defaultDimensions = getPartGroupDefaultDimensions(selectedPartGroupId);
      setHotStampingLength(defaultDimensions.length);
      setHotStampingWidth(defaultDimensions.width);
      setHotStampingDimensionsModalVisible(true);
      return;
    }

    // 凹凸工艺需要特殊处理，先输入尺寸参数
    if (processType === 'embossing') {
      setSelectedEmbossingProcess(processData);
      // 设置默认尺寸
      const defaultDimensions = getPartGroupDefaultDimensions(selectedPartGroupId);
      setEmbossingLength(defaultDimensions.length);
      setEmbossingWidth(defaultDimensions.width);
      setEmbossingDimensionsModalVisible(true);
      return;
    }

    // 液压工艺需要特殊处理，先输入尺寸参数
    if (processType === 'hydraulic') {
      setSelectedHydraulicProcess(processData);
      // 设置默认尺寸
      const defaultDimensions = getPartGroupDefaultDimensions(selectedPartGroupId);
      setHydraulicLength(defaultDimensions.length);
      setHydraulicWidth(defaultDimensions.width);
      setHydraulicDimensionsModalVisible(true);
      return;
    }



    // 根据工艺类型处理不同的数据结构
    let processItem: ProcessItem;

    if (processType === 'printing') {
      // 印刷工艺特殊处理 - 根据面纸张数计算价格
      const facePaperQuantity = getFacePaperQuantity();
      const priceResult = calculatePrintingPrice(processData, facePaperQuantity);

      processItem = {
        id: processData.id,
        name: processData.machineModel || processData.name, // 印刷工艺使用机型名称
        quantity: facePaperQuantity,
        unit: processData.unit || '张',
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'surface') {
      // 覆膜工艺特殊处理 - 根据单位类型和实际数量计算价格
      const unit = processData.unit || '元/平方';
      const calculationQuantity = getProcessCalculationQuantity(selectedPartGroupId, unit);

      const priceResult = calculateProcessPrice(processData, calculationQuantity);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: calculationQuantity,
        unit: unit,
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'laminating') {
      // 对裱工艺特殊处理 - 根据单位类型和实际数量计算价格
      const unit = processData.unit || '元/平方';
      const calculationQuantity = getProcessCalculationQuantity(selectedPartGroupId, unit);

      const priceResult = calculateProcessPrice(processData, calculationQuantity);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: calculationQuantity,
        unit: unit,
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'silkScreen') {
      // 丝网印刷工艺特殊处理 - 根据单位类型和实际数量计算价格
      const unit = processData.unit || '元/平方';
      const calculationQuantity = getProcessCalculationQuantity(selectedPartGroupId, unit);

      // 注意：丝网印刷使用 unitPrice 字段而不是 price
      const priceData = {
        ...processData,
        price: processData.unitPrice || processData.price // 统一价格字段
      };

      const priceResult = calculateProcessPrice(priceData, calculationQuantity);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: calculationQuantity,
        unit: unit,
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'texturing') {
      // 压纹工艺特殊处理 - 阶梯计价
      const boxQuantity = state.basicInfo.quantity || 1;
      const priceResult = calculateTexturingPrice(processData, boxQuantity);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: boxQuantity,
        unit: '元/张',
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'embossing') {
      // 凹凸工艺已在上面的特殊处理中处理，这里不应该到达
      perfLog.warn('凹凸工艺应该通过尺寸输入模态框处理');
      return;
    } else if (processType === 'hydraulic') {
      // 液压工艺已在上面的特殊处理中处理，这里不应该到达
      perfLog.warn('液压工艺应该通过尺寸输入模态框处理');
      return;
    } else if (processType === 'dieCutting') {
      // 模切工艺特殊处理 - 根据单位类型和实际数量计算价格
      const unit = processData.unit || '元/张';
      const calculationQuantity = getProcessCalculationQuantity(selectedPartGroupId, unit);

      const priceResult = calculateProcessPrice(processData, calculationQuantity);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: calculationQuantity,
        unit: unit,
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'dieCuttingPlateFee') {
      // 刀版费特殊处理 - 使用专门的计算函数
      const priceResult = calculateDieCuttingPlateFeePrice(processData, selectedPartGroupId);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: priceResult.quantity,
        unit: priceResult.unit,
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    } else if (processType === 'corrugated') {
      // 瓦楞工艺特殊处理 - 固定按面积计费，包含上机费
      const unit = processData.unit || '元/平方';
      const calculationQuantity = getProcessCalculationQuantity(selectedPartGroupId, unit);

      // 瓦楞工艺价格计算：基础价格 + 上机费
      const basePrice = processData.price || 0;
      const setupFee = processData.setupFee || 0;

      // 计算总费用：基础费用（按面积）+ 上机费（固定费用）
      const baseCost = basePrice * calculationQuantity;
      const totalCost = baseCost + setupFee;
      const unitPrice = calculationQuantity > 0 ? totalCost / calculationQuantity : basePrice;

      processItem = {
        id: processData.id,
        name: processData.materialName || processData.name, // 瓦楞工艺使用材质名称
        quantity: calculationQuantity,
        unit: unit,
        unitPrice: unitPrice,
        totalPrice: totalCost,
        parameters: processData
      };
    } else {
      // 其他工艺类型的通用处理 - 应用起步价逻辑
      const priceResult = calculateProcessPrice(processData, 1);

      processItem = {
        id: processData.id,
        name: processData.name,
        quantity: 1,
        unit: processData.unit || '元/平方',
        unitPrice: priceResult.unitPrice,
        totalPrice: priceResult.totalPrice,
        parameters: processData
      };
    }

    perfLog.debug('创建的工艺项目:', processItem);

    // 更新部件组工艺配置
    const updatedConfig = { ...state.processConfig };
    const partGroupConfigs = { ...updatedConfig.partGroupProcessConfigs };

    if (!partGroupConfigs[selectedPartGroupId]) {
      // 获取部件组信息
      const partGroup = getCurrentPartGroups().find(pg => pg.id === selectedPartGroupId);
      partGroupConfigs[selectedPartGroupId] = {
        partGroupId: selectedPartGroupId,
        partGroupName: partGroup?.name || '',
        materialType: partGroup?.materialType || 'face',
        groupProcessCost: 0
      };
    }

    const groupConfig = partGroupConfigs[selectedPartGroupId];

    // 根据工艺类型添加到对应的数组
    switch (processType) {
      case 'printing':
        groupConfig.printing = [...(groupConfig.printing || []), processItem];
        break;
      case 'surface':
        groupConfig.surfaceProcess = [...(groupConfig.surfaceProcess || []), processItem];
        break;
      case 'silkScreen':
        groupConfig.silkScreen = [...(groupConfig.silkScreen || []), processItem];
        break;
      case 'hotStamping':
        groupConfig.hotStamping = [...(groupConfig.hotStamping || []), processItem];
        break;
      case 'laminating':
        groupConfig.laminating = [...(groupConfig.laminating || []), processItem];
        break;
      case 'corrugated':
        groupConfig.corrugated = [...(groupConfig.corrugated || []), processItem];
        break;
      case 'texturing':
        groupConfig.embossing = [...(groupConfig.embossing || []), processItem];
        break;
      case 'embossing':
        groupConfig.embossing = [...(groupConfig.embossing || []), processItem];
        break;
      case 'hydraulic':
        groupConfig.embossing = [...(groupConfig.embossing || []), processItem];
        break;
      case 'dieCutting':
        groupConfig.dieCutting = [...(groupConfig.dieCutting || []), processItem];
        break;
      case 'dieCuttingPlateFee':
        groupConfig.dieCuttingPlateFee = [...(groupConfig.dieCuttingPlateFee || []), processItem];
        break;
    }

    // 重新计算该部件组的工艺总成本
    const groupTotalCost = Object.values(groupConfig)
      .filter((items): items is ProcessItem[] => Array.isArray(items))
      .flat()
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0);

    groupConfig.groupProcessCost = groupTotalCost;

    // 重新计算总工艺成本
    const totalCost = Object.values(partGroupConfigs)
      .reduce((sum, config) => sum + (config.groupProcessCost || 0), 0);

    updatedConfig.partGroupProcessConfigs = partGroupConfigs;
    updatedConfig.processCost = totalCost;

    onUpdate.processConfig(updatedConfig);
    setProcessModalVisible(false);
    message.success(`已添加${getProcessTypeName(processType)}`);
  };

  // 删除工艺项目 - 支持部件分组
  const handleRemoveProcess = (processType: string, itemIndex?: number, partGroupId?: string) => {
    perfLog.debug('删除工艺项目:', { processType, itemIndex, partGroupId });

    if (!partGroupId) {
      message.error('未指定部件组');
      return;
    }

    const updatedConfig = { ...state.processConfig };
    const partGroupConfigs = { ...updatedConfig.partGroupProcessConfigs };

    if (!partGroupConfigs[partGroupId]) {
      message.error('部件组配置不存在');
      return;
    }

    const groupConfig = partGroupConfigs[partGroupId];

    if (itemIndex !== undefined) {
      // 删除特定项目
      switch (processType) {
        case 'printing':
          groupConfig.printing = groupConfig.printing?.filter((_, index) => index !== itemIndex);
          break;
        case 'surface':
          groupConfig.surfaceProcess = groupConfig.surfaceProcess?.filter((_, index) => index !== itemIndex);
          break;
        case 'silkScreen':
          groupConfig.silkScreen = groupConfig.silkScreen?.filter((_, index) => index !== itemIndex);
          break;
        case 'hotStamping':
          groupConfig.hotStamping = groupConfig.hotStamping?.filter((_, index) => index !== itemIndex);
          break;
        case 'laminating':
          groupConfig.laminating = groupConfig.laminating?.filter((_, index) => index !== itemIndex);
          break;
        case 'corrugated':
          groupConfig.corrugated = groupConfig.corrugated?.filter((_, index) => index !== itemIndex);
          break;
        case 'texturing':
        case 'embossing':
        case 'hydraulic':
          groupConfig.embossing = groupConfig.embossing?.filter((_, index) => index !== itemIndex);
          break;
        case 'dieCutting':
          groupConfig.dieCutting = groupConfig.dieCutting?.filter((_, index) => index !== itemIndex);
          break;
        case 'dieCuttingPlateFee':
          groupConfig.dieCuttingPlateFee = groupConfig.dieCuttingPlateFee?.filter((_, index) => index !== itemIndex);
          break;
      }
    } else {
      // 清空所有项目
      switch (processType) {
        case 'printing':
          groupConfig.printing = [];
          break;
        case 'surface':
          groupConfig.surfaceProcess = [];
          break;
        case 'silkScreen':
          groupConfig.silkScreen = [];
          break;
        case 'hotStamping':
          groupConfig.hotStamping = [];
          break;
        case 'laminating':
          groupConfig.laminating = [];
          break;
        case 'corrugated':
          groupConfig.corrugated = [];
          break;
        case 'texturing':
        case 'embossing':
        case 'hydraulic':
          groupConfig.embossing = [];
          break;
        case 'dieCutting':
          groupConfig.dieCutting = [];
          break;
        case 'dieCuttingPlateFee':
          groupConfig.dieCuttingPlateFee = [];
          break;
      }
    }

    // 重新计算该部件组的工艺总成本
    const groupTotalCost = Object.values(groupConfig)
      .filter((items): items is ProcessItem[] => Array.isArray(items))
      .flat()
      .reduce((sum, item) => sum + (item.totalPrice || 0), 0);

    groupConfig.groupProcessCost = groupTotalCost;

    // 重新计算总工艺成本
    const totalCost = Object.values(partGroupConfigs)
      .reduce((sum, config) => sum + (config.groupProcessCost || 0), 0);

    updatedConfig.partGroupProcessConfigs = partGroupConfigs;
    updatedConfig.processCost = totalCost;

    onUpdate.processConfig(updatedConfig);
    message.success(`已删除${getProcessTypeName(processType)}`);
  };

  // 当前选择的部件组ID
  const [selectedPartGroupId, setSelectedPartGroupId] = useState<string>('');

  // 打开工艺选择模态框
  const openProcessModal = (processType: string, partGroupId?: string) => {
    setSelectedPartGroupId(partGroupId || '');

    // 凹凸工艺需要先选择子类型
    if (processType === 'embossing') {
      setEmbossingSubTypeModalVisible(true);
      return;
    }

    setSelectedProcessType(processType);
    setProcessModalVisible(true);
    fetchProcessData(processType);
  };

  // 处理凹凸工艺子类型选择
  const handleEmbossingSubTypeSelect = (subType: 'texturing' | 'embossing' | 'hydraulic') => {
    setSelectedEmbossingSubType(subType);
    setEmbossingSubTypeModalVisible(false);

    // 根据子类型打开对应的工艺选择模态框
    setSelectedProcessType(subType);
    setProcessModalVisible(true);
    fetchProcessData(subType);
  };

  // 获取印刷机当前匹配的价格区间信息
  const getPrintingMachinePriceRange = (printingData: any): {
    range: string;
    isConfigured: boolean;
    displayText: string;
  } => {
    const currentQuantity = getFacePaperQuantity();

    // 定义价格阶梯
    const priceTiers = [
      { range: '起步价', field: 'basePrice', min: 0, max: 999 },
      { range: '1000-1999', field: 'price1000_1999', min: 1000, max: 1999 },
      { range: '2000-2999', field: 'price2000_2999', min: 2000, max: 2999 },
      { range: '3000-3999', field: 'price3000_3999', min: 3000, max: 3999 },
      { range: '4000-4999', field: 'price4000_4999', min: 4000, max: 4999 },
      { range: '5000-5999', field: 'price5000_5999', min: 5000, max: 5999 },
      { range: '6000-6999', field: 'price6000_6999', min: 6000, max: 6999 },
      { range: '7000-7999', field: 'price7000_7999', min: 7000, max: 7999 },
      { range: '8000-8999', field: 'price8000_8999', min: 8000, max: 8999 },
      { range: '9000-9999', field: 'price9000_9999', min: 9000, max: 9999 },
      { range: '10000+', field: 'price10000Plus', min: 10000, max: Infinity }
    ];

    // 找到当前数量匹配的价格阶梯
    const matchedTier = priceTiers.find(tier =>
      currentQuantity >= tier.min && currentQuantity <= tier.max
    );

    if (!matchedTier) {
      return {
        range: '10000+',
        isConfigured: true,
        displayText: '10000+'
      };
    }

    // 检查当前区间价格是否已配置
    const currentPrice = Number(printingData[matchedTier.field]) || 0;
    const isConfigured = currentPrice > 0;

    let displayText: string;
    if (isConfigured) {
      displayText = matchedTier.range;
    } else {
      displayText = '未设置，使用10000+';
    }

    return {
      range: matchedTier.range,
      isConfigured,
      displayText
    };
  };

  // 工艺选择表格列定义 - 根据工艺类型动态显示字段
  const getProcessColumns = (processType: string) => {
    const baseColumns: any[] = [
      {
        title: '名称',
        dataIndex: processType === 'printing' ? 'machineModel' : 'name',
        key: 'name',
        render: (_: any, record: any) => processType === 'printing' ? record.machineModel : record.name,
      }
    ];

    // 根据工艺类型添加特定字段
    switch (processType) {
      case 'printing':
        baseColumns.push(
          {
            title: '价格区间',
            key: 'priceRange',
            width: 150,
            render: (_: any, record: any) => {
              const priceRange = getPrintingMachinePriceRange(record);

              return (
                <div style={{
                  padding: '4px 8px',
                  backgroundColor: priceRange.isConfigured ? '#f6ffed' : '#fff2e8',
                  borderRadius: '4px',
                  border: priceRange.isConfigured ? '1px solid #52c41a' : '1px solid #fa8c16',
                  textAlign: 'center'
                }}>
                  <span style={{
                    fontWeight: 'bold',
                    color: priceRange.isConfigured ? '#52c41a' : '#fa8c16',
                    fontSize: '14px'
                  }}>
                    {priceRange.displayText}
                  </span>
                </div>
              );
            },
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: 'CTP版费',
            dataIndex: 'ctpPlateFee',
            key: 'ctpPlateFee',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '专色费',
            dataIndex: 'spotColorFee',
            key: 'spotColorFee',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'surface':
        baseColumns.push(
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '厚度',
            dataIndex: 'thickness',
            key: 'thickness',
            render: (value: any) => `${Number(value) || 0}μm`,
          },
          {
            title: '密度',
            dataIndex: 'density',
            key: 'density',
            render: (value: any) => `${Number(value) || 0}g/cm³`,
          }
        );
        break;
      case 'silkScreen':
        baseColumns.push(
          {
            title: '单价',
            dataIndex: 'unitPrice',
            key: 'unitPrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '材料费',
            dataIndex: 'materialFee',
            key: 'materialFee',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'hotStamping':
        baseColumns.push(
          {
            title: '工资',
            dataIndex: 'salary',
            key: 'salary',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '工资单位',
            dataIndex: 'salaryUnit',
            key: 'salaryUnit',
          },
          {
            title: '材料价格',
            dataIndex: 'materialPrice',
            key: 'materialPrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '材料单位',
            dataIndex: 'materialUnit',
            key: 'materialUnit',
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'laminating':
        baseColumns.push(
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'corrugated':
        baseColumns.push(
          {
            title: '代号',
            dataIndex: 'code',
            key: 'code',
          },
          {
            title: '材质名称',
            dataIndex: 'materialName',
            key: 'materialName',
          },
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '厚度',
            dataIndex: 'thickness',
            key: 'thickness',
            render: (value: any) => `${Number(value) || 0}mm`,
          },
          {
            title: '上机费',
            dataIndex: 'setupFee',
            key: 'setupFee',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'texturing':
        baseColumns.push(
          {
            title: '压纹版费',
            dataIndex: 'textureVersion',
            key: 'textureVersion',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '1000以下',
            dataIndex: 'priceBelow1000',
            key: 'priceBelow1000',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '1000-1999',
            dataIndex: 'price1000_1999',
            key: 'price1000_1999',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '2000-3999',
            dataIndex: 'price2000_3999',
            key: 'price2000_3999',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '4000+',
            dataIndex: 'price4000Plus',
            key: 'price4000Plus',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'embossing':
      case 'hydraulic':
        baseColumns.push(
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '工资',
            dataIndex: 'salary',
            key: 'salary',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '工资起步价',
            dataIndex: 'salaryBasePrice',
            key: 'salaryBasePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'dieCutting':
        baseColumns.push(
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '起步价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          }
        );
        break;
      case 'dieCuttingPlateFee':
        baseColumns.push(
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
          {
            title: '起步金额',
            dataIndex: 'basePrice',
            key: 'basePrice',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '按拼版数量',
            dataIndex: 'impositionQuantity',
            key: 'impositionQuantity',
            render: (value: any) => `${Number(value) || 0}`,
          }
        );
        break;
      default:
        baseColumns.push(
          {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price: any) => `¥${(Number(price) || 0).toFixed(2)}`,
          },
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          }
        );
    }

    // 添加操作列
    baseColumns.push({
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button
          type="primary"
          size="small"
          onClick={() => handleAddProcess(record, processType)}
        >
          选择
        </Button>
      ),
    });

    return baseColumns;
  };

  return (
    <div className="process-accessory-step">
      <Alert
        message="工艺选择"
        description="选择印刷工艺、后道工艺等，系统将根据选择计算相应费用。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Row gutter={[0, 16]}>
        <Col span={24}>
          <Card
            title={
              <Space>
                <SettingOutlined />
                <span>工艺选择</span>
              </Space>
            }
            style={{ height: '100%' }}
          >
            <ProcessSelectionPanel
              processConfig={state.processConfig}
              partGroups={getCurrentPartGroups()}
              partMaterialConfigs={state.materialConfig?.partMaterialConfigs}
              getPartGroupSheetsNeeded={getPartGroupSheetsNeeded}
              onAddProcess={openProcessModal}
              onRemoveProcess={handleRemoveProcess}
              loading={loading}
            />
          </Card>
        </Col>


      </Row>

      {/* 工艺选择模态框 */}
      <Modal
        title={`选择${getProcessTypeName(selectedProcessType)}`}
        open={processModalVisible}
        onCancel={() => setProcessModalVisible(false)}
        footer={null}
        width={selectedProcessType === 'printing' ? 1000 : 800} // 印刷工艺模态框
      >
        <ProcessSelectionModal
          processType={selectedProcessType}
          printingList={printingList}
          surfaceProcessList={surfaceProcessList}
          silkScreenList={silkScreenList}
          hotStampingList={hotStampingList}
          laminatingList={laminatingList}
          corrugatedList={corrugatedList}
          embossingList={embossingList}
          texturingList={texturingList}
          hydraulicList={hydraulicList}
          dieCuttingList={dieCuttingList}
          dieCuttingPlateFeeList={dieCuttingPlateFeeList}
          columns={getProcessColumns(selectedProcessType)}
          loading={loading}
        />
      </Modal>



      {/* 烫金工艺尺寸参数输入模态框 */}
      <Modal
        title="烫金工艺尺寸参数"
        open={hotStampingDimensionsModalVisible}
        onOk={handleHotStampingDimensionsConfirm}
        onCancel={() => {
          setHotStampingDimensionsModalVisible(false);
          setSelectedHotStampingProcess(null);
          setHotStampingLength(0);
          setHotStampingWidth(0);
        }}
        okText="确认添加"
        cancelText="取消"
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="烫金工艺参数设置"
            description="请输入烫金区域的长度和宽度尺寸（单位：毫米）"
            type="info"
            style={{ marginBottom: 16 }}
          />

          {selectedHotStampingProcess && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Typography.Text strong>选择的烫金工艺：</Typography.Text>
              <Typography.Text>{selectedHotStampingProcess.name}</Typography.Text>
            </Card>
          )}

          <Form layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="长度 (mm)" required>
                  <InputNumber
                    value={hotStampingLength}
                    onChange={(value) => setHotStampingLength(value || 0)}
                    min={0}
                    precision={1}
                    style={{ width: '100%' }}
                    placeholder="请输入长度"
                    addonAfter="mm"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="宽度 (mm)" required>
                  <InputNumber
                    value={hotStampingWidth}
                    onChange={(value) => setHotStampingWidth(value || 0)}
                    min={0}
                    precision={1}
                    style={{ width: '100%' }}
                    placeholder="请输入宽度"
                    addonAfter="mm"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>

      {/* 凹凸工艺子类型选择模态框 */}
      <Modal
        title="选择凹凸工艺类型"
        open={embossingSubTypeModalVisible}
        onCancel={() => setEmbossingSubTypeModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="凹凸工艺类型选择"
            description="请选择具体的凹凸工艺类型，不同类型有不同的计价方式"
            type="info"
            style={{ marginBottom: 24 }}
          />

          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card
                hoverable
                style={{ textAlign: 'center', cursor: 'pointer' }}
                onClick={() => handleEmbossingSubTypeSelect('texturing')}
              >
                <div style={{ padding: '20px 0' }}>
                  <div style={{ fontSize: '32px', marginBottom: 8 }}>🔨</div>
                  <Title level={4}>压纹工艺</Title>
                  <Text type="secondary">阶梯计价模式</Text>
                  <br />
                  <Text type="secondary">压纹版费 + 阶梯价格</Text>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card
                hoverable
                style={{ textAlign: 'center', cursor: 'pointer' }}
                onClick={() => handleEmbossingSubTypeSelect('embossing')}
              >
                <div style={{ padding: '20px 0' }}>
                  <div style={{ fontSize: '32px', marginBottom: 8 }}>⚡</div>
                  <Title level={4}>凹凸工艺</Title>
                  <Text type="secondary">材料费 + 工资费</Text>
                  <br />
                  <Text type="secondary">支持起步价判断</Text>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card
                hoverable
                style={{ textAlign: 'center', cursor: 'pointer' }}
                onClick={() => handleEmbossingSubTypeSelect('hydraulic')}
              >
                <div style={{ padding: '20px 0' }}>
                  <div style={{ fontSize: '32px', marginBottom: 8 }}>💧</div>
                  <Title level={4}>液压工艺</Title>
                  <Text type="secondary">材料费 + 工资费</Text>
                  <br />
                  <Text type="secondary">与凹凸工艺计算相同</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </Modal>

      {/* 凹凸工艺尺寸参数输入模态框 */}
      <Modal
        title="凹凸工艺尺寸参数"
        open={embossingDimensionsModalVisible}
        onOk={handleEmbossingDimensionsConfirm}
        onCancel={() => {
          setEmbossingDimensionsModalVisible(false);
          setSelectedEmbossingProcess(null);
          setEmbossingLength(0);
          setEmbossingWidth(0);
        }}
        okText="确认添加"
        cancelText="取消"
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="凹凸工艺参数设置"
            description="请输入凹凸区域的长度和宽度尺寸（单位：毫米）"
            type="info"
            style={{ marginBottom: 16 }}
          />

          {selectedEmbossingProcess && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Typography.Text strong>选择的凹凸工艺：</Typography.Text>
              <Typography.Text>{selectedEmbossingProcess.name}</Typography.Text>
            </Card>
          )}

          <Form layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="长度 (mm)" required>
                  <InputNumber
                    value={embossingLength}
                    onChange={(value) => setEmbossingLength(value || 0)}
                    min={0}
                    precision={1}
                    style={{ width: '100%' }}
                    placeholder="请输入长度"
                    addonAfter="mm"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="宽度 (mm)" required>
                  <InputNumber
                    value={embossingWidth}
                    onChange={(value) => setEmbossingWidth(value || 0)}
                    min={0}
                    precision={1}
                    style={{ width: '100%' }}
                    placeholder="请输入宽度"
                    addonAfter="mm"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>

      {/* 液压工艺尺寸参数输入模态框 */}
      <Modal
        title="液压工艺尺寸参数"
        open={hydraulicDimensionsModalVisible}
        onOk={handleHydraulicDimensionsConfirm}
        onCancel={() => {
          setHydraulicDimensionsModalVisible(false);
          setSelectedHydraulicProcess(null);
          setHydraulicLength(0);
          setHydraulicWidth(0);
        }}
        okText="确认添加"
        cancelText="取消"
        width={500}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="液压工艺参数设置"
            description="请输入液压区域的长度和宽度尺寸（单位：毫米）"
            type="info"
            style={{ marginBottom: 16 }}
          />

          {selectedHydraulicProcess && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Typography.Text strong>选择的液压工艺：</Typography.Text>
              <Typography.Text>{selectedHydraulicProcess.name}</Typography.Text>
            </Card>
          )}

          <Form layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="长度 (mm)" required>
                  <InputNumber
                    value={hydraulicLength}
                    onChange={(value) => setHydraulicLength(value || 0)}
                    min={0}
                    precision={1}
                    style={{ width: '100%' }}
                    placeholder="请输入长度"
                    addonAfter="mm"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="宽度 (mm)" required>
                  <InputNumber
                    value={hydraulicWidth}
                    onChange={(value) => setHydraulicWidth(value || 0)}
                    min={0}
                    precision={1}
                    style={{ width: '100%' }}
                    placeholder="请输入宽度"
                    addonAfter="mm"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

// 工艺选择面板组件
interface ProcessSelectionPanelProps {
  processConfig: ProcessConfig;
  partGroups: PartGroup[]; // 部件分组信息
  partMaterialConfigs?: Record<string, PartMaterialConfig>; // 部件材料配置
  getPartGroupSheetsNeeded?: (partGroupId: string) => number | undefined; // 获取部件分组材料张数的函数
  onAddProcess: (processType: string, partGroupId?: string) => void;
  onRemoveProcess: (processType: string, itemIndex?: number, partGroupId?: string) => void;
  loading: boolean;
}

const ProcessSelectionPanel: React.FC<ProcessSelectionPanelProps> = ({
  processConfig,
  partGroups,
  partMaterialConfigs,
  getPartGroupSheetsNeeded,
  onAddProcess,
  onRemoveProcess}) => {
  // 根据材料类型获取可选工艺类型
  const getAvailableProcessTypes = (materialType: 'face' | 'grey') => {
    if (materialType === 'face') {
      return [
        { key: 'printing', name: '印刷工艺', icon: '🖨️' },
        { key: 'surface', name: '覆膜工艺', icon: '✨' },
        { key: 'laminating', name: '对裱工艺', icon: '📄' },
        { key: 'silkScreen', name: '丝网印刷', icon: '🎨' },
        { key: 'embossing', name: '凹凸工艺', icon: '🔨' },
        { key: 'hotStamping', name: '烫金工艺', icon: '🏆' },
        { key: 'dieCutting', name: '模切工艺', icon: '✂️' }
      ];
    } else {
      return [
        { key: 'dieCutting', name: '模切工艺', icon: '✂️' }
      ];
    }
  };

  // 获取部件组的工艺配置
  const getPartGroupProcessConfig = (partGroupId: string) => {
    return processConfig.partGroupProcessConfigs?.[partGroupId];
  };

  // 检查是否有模切工艺（用于显示刀版费选项）
  const hasPartGroupDieCutting = (partGroupId: string): boolean => {
    const groupConfig = getPartGroupProcessConfig(partGroupId);
    return !!(groupConfig?.dieCutting && groupConfig.dieCutting.length > 0);
  };

  // 获取工艺类型对应的字段名
  const getProcessFieldName = (processType: string): string => {
    const fieldMapping = {
      'printing': 'printing',
      'surface': 'surfaceProcess',
      'silkScreen': 'silkScreen',
      'hotStamping': 'hotStamping',
      'laminating': 'laminating',
      'corrugated': 'corrugated',
      'embossing': 'embossing',
      'dieCutting': 'dieCutting',
      'dieCuttingPlateFee': 'dieCuttingPlateFee'
    };
    return fieldMapping[processType as keyof typeof fieldMapping] || processType;
  };

  // 渲染部件组工艺卡片的函数
  const renderPartGroupProcessCard = (
    processType: { key: string; name: string; icon: string },
    partGroupId: string
  ) => {
    const { key, name, icon } = processType;
    const groupConfig = getPartGroupProcessConfig(partGroupId);
    const fieldName = getProcessFieldName(key);
    const processItems = groupConfig?.[fieldName as keyof typeof groupConfig] as ProcessItem[] | undefined;
    const hasItems = processItems && processItems.length > 0;

    // 检查是否显示刀版费选项（只有选择了模切工艺才显示）
    const showPlateFeeOption = key === 'dieCuttingPlateFee' && !hasPartGroupDieCutting(partGroupId);

    if (showPlateFeeOption) {
      return null; // 如果没有选择模切工艺，不显示刀版费选项
    }

    return (
      <Col xs={24} sm={12} md={8} key={`${partGroupId}-${key}`}>
        <Card
          size="small"
          title={
            <span>
              <span style={{ marginRight: 8 }}>{icon}</span>
              {name}
              {hasItems && <span style={{ marginLeft: 8, color: '#1890ff' }}>({processItems.length})</span>}
            </span>
          }
          extra={
            <Space>
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={() => onAddProcess(key, partGroupId)}
              >
                添加
              </Button>
            </Space>
          }
        >
          {hasItems ? (
            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {processItems.map((item, index) => (
                <div key={index} style={{ marginBottom: 8, padding: 8, border: '1px solid #f0f0f0', borderRadius: 4 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <Text strong>{item.name}</Text>
                      <br />
                          <Text type="secondary">
                            {(item.unitPrice || 0).toFixed(2)} {item.unit}
                          </Text>
                          <br />
                      <Text type="success">
                        总价: ¥{(item.totalPrice || 0).toFixed(2)}
                      </Text>
                      {/* 显示烫金工艺的长宽参数 */}
                      {key === 'hotStamping' && item.hotStampingDimensions && (
                        <>
                          <br />
                          <Text type="secondary">
                            尺寸: {item.hotStampingDimensions.length}×{item.hotStampingDimensions.width}mm
                          </Text>
                        </>
                      )}
                      {/* 显示凹凸工艺的长宽参数 */}
                      {key === 'embossing' && item.embossingDimensions && (
                        <>
                          <br />
                          <Text type="secondary">
                            尺寸: {item.embossingDimensions.length}×{item.embossingDimensions.width}mm
                          </Text>
                        </>
                      )}
                      {/* 显示液压工艺的长宽参数（液压工艺也存储在embossing字段中，通过名称区分） */}
                      {key === 'embossing' && item.hydraulicDimensions && (
                        <>
                          <br />
                          <Text type="secondary">
                            尺寸: {item.hydraulicDimensions.length}×{item.hydraulicDimensions.width}mm
                          </Text>
                        </>
                      )}
                    </div>
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => onRemoveProcess(key, index, partGroupId)}
                      title="删除此项"
                    />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Text type="secondary">未选择{name}</Text>
          )}
        </Card>
      </Col>
    );
  };

  return (
    <div>
      {/* 按部件分组显示工艺选择 */}
      {partGroups.map((partGroup) => {
        const availableProcessTypes = getAvailableProcessTypes(partGroup.materialType);
        const materialTypeLabel = partGroup.materialType === 'face' ? '面纸' : '灰板纸';
        const tagColor = partGroup.materialType === 'face' ? 'blue' : 'green';

        return (
          <Card
            key={partGroup.id}
            title={
              <Space>
                <Tag color={tagColor}>{materialTypeLabel}</Tag>
                <span>{partGroup.name}</span>
              </Space>
            }
            style={{ marginBottom: 16 }}
            size="small"
          >
            {/* 添加材料信息显示 */}
            <MaterialInfoDisplay
              partGroupId={partGroup.id}
              materialType={partGroup.materialType}
              partMaterialConfigs={partMaterialConfigs}
              sheetsNeeded={getPartGroupSheetsNeeded?.(partGroup.id)}
            />

            <Row gutter={[16, 16]}>
              {availableProcessTypes.map((processType) =>
                renderPartGroupProcessCard(processType, partGroup.id)
              )}
              {/* 如果选择了模切工艺，显示刀版费选项 */}
              {hasPartGroupDieCutting(partGroup.id) &&
                renderPartGroupProcessCard(
                  { key: 'dieCuttingPlateFee', name: '刀版费', icon: '🔪' },
                  partGroup.id
                )
              }
            </Row>
          </Card>
        );
      })}

      {/* 如果没有部件分组，显示提示信息 */}
      {partGroups.length === 0 && (
        <Alert
          message="等待部件分组"
          description="请先在步骤二中完成部件合并和材料选择，然后返回此步骤选择工艺。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {processConfig.processCost !== undefined && (
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Title level={4}>
            工艺总费用: <Text type="success">¥{(processConfig.processCost || 0).toFixed(2)}</Text>
          </Title>
        </div>
      )}
    </div>
  );
};

// 工艺选择模态框组件
interface ProcessSelectionModalProps {
  processType: string;
  printingList: Printing[];
  surfaceProcessList: SurfaceProcess[];
  silkScreenList: SilkScreenProcess[];
  hotStampingList: HotStampingProcess[];
  laminatingList: LaminatingProcess[];
  corrugatedList: CorrugatedProcess[];
  embossingList: EmbossingProcess[];
  texturingList: TexturingProcess[];
  hydraulicList: HydraulicProcess[];
  dieCuttingList: DieCuttingProcess[];
  dieCuttingPlateFeeList: DieCuttingPlateFee[];
  columns: any[];
  loading: boolean;
}

const ProcessSelectionModal: React.FC<ProcessSelectionModalProps> = ({
  processType,
  printingList,
  surfaceProcessList,
  silkScreenList,
  hotStampingList,
  laminatingList,
  corrugatedList,
  embossingList,
  texturingList,
  hydraulicList,
  dieCuttingList,
  dieCuttingPlateFeeList,
  columns,
  loading
}) => {
  const getDataSource = () => {
    switch (processType) {
      case 'printing':
        return printingList;
      case 'surface':
        return surfaceProcessList;
      case 'silkScreen':
        return silkScreenList;
      case 'hotStamping':
        return hotStampingList;
      case 'laminating':
        return laminatingList;
      case 'corrugated':
        return corrugatedList;
      case 'embossing':
        return embossingList;
      case 'texturing':
        return texturingList;
      case 'hydraulic':
        return hydraulicList;
      case 'dieCutting':
        return dieCuttingList;
      case 'dieCuttingPlateFee':
        return dieCuttingPlateFeeList;
      default:
        return [];
    }
  };

  return (
    <Table
      dataSource={getDataSource() as any}
      columns={columns}
      rowKey="id"
      loading={loading}
      pagination={{ pageSize: 10 }}
    />
  );
};

export default ProcessStep;


