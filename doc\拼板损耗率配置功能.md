# 拼板损耗率配置功能

## 功能概述

在盒型计算系统中新增了拼板参数配置功能，包括面纸损耗率、灰板损耗率和特种纸损耗率的可配置参数。用户可以在拼板参数配置界面中自定义损耗率，系统将在材料成本计算中应用这些配置的损耗率。

## 实现内容

### 1. 类型定义扩展

**文件：** `src/app/admin/box/calculation/types/calculation.ts`

在 `PackagingConfig` 接口中添加了损耗率配置参数：

```typescript
// 损耗率配置参数
paperWasteRate?: number;         // 面纸损耗率（默认10%）
specialPaperWasteRate?: number;  // 特种纸损耗率（默认5%）
greyBoardWasteRate?: number;     // 灰板损耗率（默认8%）
```

### 2. 拼板参数配置界面

**文件：** `src/app/admin/box/calculation/components/steps/PackagingStep.tsx`

#### 2.1 默认值常量
添加了损耗率的默认值常量：

```typescript
const DEFAULT_VALUES = {
  BLEED: 4,
  GRIPPER: 10,
  BITE: 15,
  MARGIN_LENGTH: 3,
  MARGIN_WIDTH: 3,
  PAPER_WASTE_RATE: 10,      // 面纸损耗率 10%
  SPECIAL_PAPER_WASTE_RATE: 5, // 特种纸损耗率 5%
  GREY_BOARD_WASTE_RATE: 8   // 灰板损耗率 8%
} as const;
```

#### 2.2 用户界面
在拼板参数配置卡片中添加了损耗率配置输入框：

- 面纸损耗率：范围 0-50%，精度 0.1%，默认值 10%
- 特种纸损耗率：范围 0-50%，精度 0.1%，默认值 5%
- 灰板损耗率：范围 0-50%，精度 0.1%，默认值 8%

每个输入框都配有工具提示，说明损耗率的用途。

#### 2.3 参数传递
将损耗率参数传递给 `ImpositionLogicDisplay` 组件：

```typescript
paperWasteRate={state.packagingConfig.paperWasteRate ?? DEFAULT_VALUES.PAPER_WASTE_RATE}
specialPaperWasteRate={state.packagingConfig.specialPaperWasteRate ?? DEFAULT_VALUES.SPECIAL_PAPER_WASTE_RATE}
greyBoardWasteRate={state.packagingConfig.greyBoardWasteRate ?? DEFAULT_VALUES.GREY_BOARD_WASTE_RATE}
```

### 3. 材料成本计算逻辑更新

**文件：** `src/app/admin/box/calculation/components/ImpositionLogicDisplay.tsx`

#### 3.1 接口扩展
在 `ImpositionLogicDisplayProps` 接口中添加了损耗率参数：

```typescript
// 损耗率参数
paperWasteRate?: number; // 面纸损耗率（%）
specialPaperWasteRate?: number; // 特种纸损耗率（%）
greyBoardWasteRate?: number; // 灰板损耗率（%）
```

#### 3.2 损耗率应用逻辑
在材料费用计算中添加了损耗率应用逻辑：

```typescript
// 根据材料类型获取对应的损耗率
const getWasteRate = (materialCategory: string): number => {
  switch (materialCategory) {
    case 'paper':
      return paperWasteRate / 100; // 转换百分比为小数
    case 'specialPaper':
      return specialPaperWasteRate / 100;
    case 'greyBoard':
      return greyBoardWasteRate / 100;
    case 'corrugated':
      return greyBoardWasteRate / 100; // 瓦楞材料使用灰板损耗率
    default:
      return paperWasteRate / 100; // 默认使用面纸损耗率
  }
};
```

#### 3.3 费用计算更新
在所有材料费用计算中应用损耗率：

```typescript
// 应用损耗率到总成本
totalCost = quantity * unitPrice * (1 + wasteRate);
```

支持的计价单位：
- 元/张：张数 × 单价 × (1 + 损耗率)
- 元/平方：面积(m²) × 单价 × (1 + 损耗率)
- 元/吨：重量(吨) × 单价 × (1 + 损耗率)

### 4. 计算引擎更新

**文件：** `src/app/admin/box/calculation/utils/calculationEngine.ts`

移除了硬编码的损耗率，改为从配置参数中获取：

#### 4.1 面纸费用计算
```typescript
private calculatePaperCost(paper: any, packagingConfig: PackagingConfig): number {
  const basePrice = paper.unitPrice * paper.quantity;
  // 从配置中获取面纸损耗率，如果没有配置则使用默认值10%
  const wasteRate = (packagingConfig.paperWasteRate ?? 10) / 100;
  return basePrice * (1 + wasteRate);
}
```

#### 4.2 特种纸费用计算
```typescript
private calculateSpecialPaperCost(specialPaper: any, packagingConfig: PackagingConfig): number {
  const basePrice = specialPaper.unitPrice * specialPaper.quantity;
  // 从配置中获取特种纸损耗率，如果没有配置则使用默认值5%
  const wasteRate = (packagingConfig.specialPaperWasteRate ?? 5) / 100;
  return basePrice * (1 + wasteRate);
}
```

#### 4.3 灰板费用计算
```typescript
private calculateGreyBoardCost(greyBoard: any, packagingConfig: PackagingConfig): number {
  const basePrice = greyBoard.unitPrice * greyBoard.quantity;
  // 从配置中获取灰板损耗率，如果没有配置则使用默认值8%
  const wasteRate = (packagingConfig.greyBoardWasteRate ?? 8) / 100;
  return basePrice * (1 + wasteRate);
}
```

## 使用说明

### 1. 配置损耗率
1. 在盒型计算的第二步"拼版参数"中
2. 找到"损耗率配置"部分
3. 根据实际需要调整各种材料的损耗率
4. 损耗率范围：0-50%，支持一位小数精度

### 2. 损耗率应用
- 配置的损耗率会自动应用到材料成本计算中
- 不同材料类型使用对应的损耗率：
  - 面纸材料：使用面纸损耗率
  - 特种纸材料：使用特种纸损耗率
  - 灰板材料：使用灰板损耗率
  - 瓦楞材料：使用灰板损耗率

### 3. 默认值
如果用户没有配置损耗率，系统将使用以下默认值：
- 面纸损耗率：10%
- 特种纸损耗率：5%
- 灰板损耗率：8%

## 技术特点

### 1. 类型安全
- 所有损耗率参数都有明确的 TypeScript 类型定义
- 使用可选参数（?:）确保向后兼容性
- 避免使用 any 类型，保持代码类型安全

### 2. 参数校验
- 输入框限制损耗率范围为 0-50%
- 支持一位小数精度
- 提供默认值确保系统稳定性

### 3. 向后兼容
- 新增的损耗率参数都是可选的
- 如果没有配置，使用合理的默认值
- 不影响现有功能的正常运行

### 4. 集成性
- 损耗率配置与现有拼板计算流程无缝集成
- 实时应用到材料成本计算中
- 支持所有材料类型和计价单位

## 注意事项

1. **参数同步**：损耗率参数在整个计算流程中保持一致性
2. **实时更新**：修改损耗率后需要重新计算拼版以应用新的参数
3. **材料分类**：确保材料正确分类以应用对应的损耗率
4. **精度控制**：损耗率支持一位小数，满足实际业务需求
